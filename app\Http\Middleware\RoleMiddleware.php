<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class RoleMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     * @param  string  $role
     */
    public function handle(Request $request, Closure $next, string $role): Response
    {
        if (!Auth::check()) {
            if ($request->expectsJson()) {
                return response()->json(['message' => 'Unauthenticated'], 401);
            }
            return redirect()->route('login');
        }

        $user = Auth::user();

        // Check if user has the required role
        if (!$this->hasRole($user, $role)) {
            if ($request->expectsJson()) {
                return response()->json([
                    'message' => 'Unauthorized. You do not have permission to access this resource.'
                ], 403);
            }

            // Redirect to appropriate dashboard based on user's actual role
            $redirectRoute = match($user->role) {
                'admin' => 'admin.dashboard',
                'seller' => 'seller.dashboard',
                'buyer' => 'buyer.dashboard',
                default => 'home'
            };

            return redirect()->route($redirectRoute)
                ->with('error', 'You do not have permission to access that page.');
        }

        // Additional checks for sellers
        if ($role === 'seller' && $user->isSeller()) {
            $seller = $user->seller;

            // Check if seller is verified for certain routes
            if ($this->requiresVerification($request) && !$seller->isVerified()) {
                if ($request->expectsJson()) {
                    return response()->json([
                        'message' => 'Your seller account is not yet verified.'
                    ], 403);
                }

                return redirect()->route('seller.verification')
                    ->with('warning', 'Please complete your seller verification to access this feature.');
            }

            // Check if seller is active
            if (!$seller->is_active) {
                if ($request->expectsJson()) {
                    return response()->json([
                        'message' => 'Your seller account is inactive.'
                    ], 403);
                }

                return redirect()->route('seller.dashboard')
                    ->with('error', 'Your seller account is inactive. Please contact support.');
            }
        }

        return $next($request);
    }

    /**
     * Check if user has the required role
     */
    protected function hasRole($user, string $role): bool
    {
        // Handle multiple roles (e.g., 'admin|seller')
        $allowedRoles = explode('|', $role);

        return in_array($user->role, $allowedRoles);
    }

    /**
     * Check if the current route requires seller verification
     */
    protected function requiresVerification(Request $request): bool
    {
        $verificationRequiredRoutes = [
            'seller.products.*',
            'seller.orders.*',
            'seller.earnings.*',
            'seller.withdrawals.*',
        ];

        $currentRoute = $request->route()->getName();

        foreach ($verificationRequiredRoutes as $pattern) {
            if (fnmatch($pattern, $currentRoute)) {
                return true;
            }
        }

        return false;
    }
}
