<?php $__env->startSection('title', 'Dispute Management'); ?>

<?php $__env->startSection('content'); ?>
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-exclamation-triangle me-2"></i>Dispute Management</h2>
    <div class="btn-group">
        <a href="<?php echo e(route('admin.disputes.index')); ?>" class="btn btn-outline-primary <?php echo e(!request('status') ? 'active' : ''); ?>">All Disputes</a>
        <a href="<?php echo e(route('admin.disputes.index', ['status' => 'open'])); ?>" class="btn btn-outline-danger <?php echo e(request('status') == 'open' ? 'active' : ''); ?>">Open</a>
        <a href="<?php echo e(route('admin.disputes.index', ['status' => 'investigating'])); ?>" class="btn btn-outline-warning <?php echo e(request('status') == 'investigating' ? 'active' : ''); ?>">Investigating</a>
        <a href="<?php echo e(route('admin.disputes.index', ['status' => 'resolved'])); ?>" class="btn btn-outline-success <?php echo e(request('status') == 'resolved' ? 'active' : ''); ?>">Resolved</a>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-2 mb-3">
        <div class="card stat-card text-center">
            <div class="card-body">
                <div class="text-primary mb-2">
                    <i class="fas fa-exclamation-triangle fa-2x"></i>
                </div>
                <h3 class="mb-0"><?php echo e($stats['total_disputes']); ?></h3>
                <small class="text-muted">Total Disputes</small>
            </div>
        </div>
    </div>
    
    <div class="col-md-2 mb-3">
        <div class="card stat-card text-center">
            <div class="card-body">
                <div class="text-danger mb-2">
                    <i class="fas fa-folder-open fa-2x"></i>
                </div>
                <h3 class="mb-0"><?php echo e($stats['open_disputes']); ?></h3>
                <small class="text-muted">Open</small>
            </div>
        </div>
    </div>
    
    <div class="col-md-2 mb-3">
        <div class="card stat-card text-center">
            <div class="card-body">
                <div class="text-warning mb-2">
                    <i class="fas fa-search fa-2x"></i>
                </div>
                <h3 class="mb-0"><?php echo e($stats['investigating_disputes']); ?></h3>
                <small class="text-muted">Investigating</small>
            </div>
        </div>
    </div>
    
    <div class="col-md-2 mb-3">
        <div class="card stat-card text-center">
            <div class="card-body">
                <div class="text-success mb-2">
                    <i class="fas fa-check-circle fa-2x"></i>
                </div>
                <h3 class="mb-0"><?php echo e($stats['resolved_disputes']); ?></h3>
                <small class="text-muted">Resolved</small>
            </div>
        </div>
    </div>
    
    <div class="col-md-2 mb-3">
        <div class="card stat-card text-center">
            <div class="card-body">
                <div class="text-danger mb-2">
                    <i class="fas fa-fire fa-2x"></i>
                </div>
                <h3 class="mb-0"><?php echo e($stats['high_priority']); ?></h3>
                <small class="text-muted">High Priority</small>
            </div>
        </div>
    </div>
    
    <div class="col-md-2 mb-3">
        <div class="card stat-card text-center">
            <div class="card-body">
                <div class="text-info mb-2">
                    <i class="fas fa-clock fa-2x"></i>
                </div>
                <h3 class="mb-0"><?php echo e($stats['avg_resolution_time']); ?></h3>
                <small class="text-muted">Avg Resolution</small>
            </div>
        </div>
    </div>
</div>

<!-- Search and Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" action="<?php echo e(route('admin.disputes.index')); ?>" class="row g-3">
            <div class="col-md-4">
                <label for="search" class="form-label">Search Disputes</label>
                <input type="text" class="form-control" id="search" name="search" 
                       value="<?php echo e(request('search')); ?>" placeholder="Dispute #, buyer, seller, subject...">
            </div>
            <div class="col-md-2">
                <label for="status" class="form-label">Status</label>
                <select class="form-select" id="status" name="status">
                    <option value="">All Statuses</option>
                    <option value="open" <?php echo e(request('status') == 'open' ? 'selected' : ''); ?>>Open</option>
                    <option value="investigating" <?php echo e(request('status') == 'investigating' ? 'selected' : ''); ?>>Investigating</option>
                    <option value="resolved" <?php echo e(request('status') == 'resolved' ? 'selected' : ''); ?>>Resolved</option>
                    <option value="closed" <?php echo e(request('status') == 'closed' ? 'selected' : ''); ?>>Closed</option>
                </select>
            </div>
            <div class="col-md-2">
                <label for="priority" class="form-label">Priority</label>
                <select class="form-select" id="priority" name="priority">
                    <option value="">All Priorities</option>
                    <option value="high" <?php echo e(request('priority') == 'high' ? 'selected' : ''); ?>>High</option>
                    <option value="medium" <?php echo e(request('priority') == 'medium' ? 'selected' : ''); ?>>Medium</option>
                    <option value="low" <?php echo e(request('priority') == 'low' ? 'selected' : ''); ?>>Low</option>
                </select>
            </div>
            <div class="col-md-2">
                <label for="date_range" class="form-label">Date Range</label>
                <select class="form-select" id="date_range" name="date_range">
                    <option value="">All Time</option>
                    <option value="today">Today</option>
                    <option value="week">This Week</option>
                    <option value="month">This Month</option>
                </select>
            </div>
            <div class="col-md-2 d-flex align-items-end">
                <button type="submit" class="btn btn-primary me-2">
                    <i class="fas fa-search"></i> Search
                </button>
                <a href="<?php echo e(route('admin.disputes.index')); ?>" class="btn btn-outline-secondary">
                    <i class="fas fa-times"></i> Clear
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Disputes Table -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">Disputes List</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>Dispute #</th>
                        <th>Order #</th>
                        <th>Parties</th>
                        <th>Subject</th>
                        <th>Amount</th>
                        <th>Priority</th>
                        <th>Status</th>
                        <th>Created</th>
                        <th>Last Activity</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php $__empty_1 = true; $__currentLoopData = $disputes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $dispute): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr>
                            <td>
                                <strong><?php echo e($dispute->dispute_number); ?></strong>
                            </td>
                            <td>
                                <a href="#" class="text-decoration-none"><?php echo e($dispute->order_number); ?></a>
                            </td>
                            <td>
                                <div class="small">
                                    <div><strong>Buyer:</strong> <?php echo e($dispute->buyer_name); ?></div>
                                    <div><strong>Seller:</strong> <?php echo e($dispute->seller_name); ?></div>
                                </div>
                            </td>
                            <td>
                                <div class="text-truncate" style="max-width: 200px;" title="<?php echo e($dispute->subject); ?>">
                                    <?php echo e($dispute->subject); ?>

                                </div>
                            </td>
                            <td>$<?php echo e(number_format($dispute->amount, 2)); ?></td>
                            <td>
                                <?php
                                    $priorityClasses = [
                                        'high' => 'danger',
                                        'medium' => 'warning',
                                        'low' => 'info'
                                    ];
                                    $priorityIcons = [
                                        'high' => 'fire',
                                        'medium' => 'exclamation',
                                        'low' => 'info-circle'
                                    ];
                                ?>
                                <span class="badge bg-<?php echo e($priorityClasses[$dispute->priority] ?? 'secondary'); ?>">
                                    <i class="fas fa-<?php echo e($priorityIcons[$dispute->priority] ?? 'question'); ?> me-1"></i>
                                    <?php echo e(ucfirst($dispute->priority)); ?>

                                </span>
                            </td>
                            <td>
                                <?php
                                    $statusClasses = [
                                        'open' => 'danger',
                                        'investigating' => 'warning',
                                        'resolved' => 'success',
                                        'closed' => 'secondary'
                                    ];
                                ?>
                                <span class="badge bg-<?php echo e($statusClasses[$dispute->status] ?? 'secondary'); ?>">
                                    <?php echo e(ucfirst($dispute->status)); ?>

                                </span>
                            </td>
                            <td><?php echo e($dispute->created_at->format('M j, Y')); ?></td>
                            <td>
                                <small class="text-muted"><?php echo e($dispute->last_activity->diffForHumans()); ?></small>
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <a href="<?php echo e(route('admin.disputes.show', $dispute->id)); ?>" 
                                       class="btn btn-outline-primary" title="View Details">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <button type="button" class="btn btn-outline-secondary dropdown-toggle dropdown-toggle-split" 
                                            data-bs-toggle="dropdown">
                                        <span class="visually-hidden">Toggle Dropdown</span>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="<?php echo e(route('admin.disputes.show', $dispute->id)); ?>">
                                            <i class="fas fa-eye me-2"></i>View Details
                                        </a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <?php if($dispute->status == 'open'): ?>
                                            <li><a class="dropdown-item" href="#" onclick="updateStatus(<?php echo e($dispute->id); ?>, 'investigating')">
                                                <i class="fas fa-search me-2"></i>Start Investigation
                                            </a></li>
                                        <?php endif; ?>
                                        <?php if($dispute->status != 'resolved'): ?>
                                            <li><a class="dropdown-item" href="#" onclick="resolveDispute(<?php echo e($dispute->id); ?>)">
                                                <i class="fas fa-check me-2"></i>Mark Resolved
                                            </a></li>
                                        <?php endif; ?>
                                        <li><a class="dropdown-item" href="#" onclick="escalateDispute(<?php echo e($dispute->id); ?>)">
                                            <i class="fas fa-arrow-up me-2"></i>Escalate Priority
                                        </a></li>
                                    </ul>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="10" class="text-center py-4">
                                <i class="fas fa-exclamation-triangle fa-3x text-muted mb-3"></i>
                                <p class="text-muted">No disputes found matching your criteria.</p>
                            </td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mt-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Quick Actions</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <button type="button" class="btn btn-outline-warning w-100" onclick="bulkInvestigate()">
                            <i class="fas fa-search me-2"></i>Bulk Investigate
                        </button>
                    </div>
                    <div class="col-md-3">
                        <button type="button" class="btn btn-outline-success w-100" onclick="bulkResolve()">
                            <i class="fas fa-check me-2"></i>Bulk Resolve
                        </button>
                    </div>
                    <div class="col-md-3">
                        <button type="button" class="btn btn-outline-info w-100" onclick="exportDisputes()">
                            <i class="fas fa-download me-2"></i>Export Report
                        </button>
                    </div>
                    <div class="col-md-3">
                        <button type="button" class="btn btn-outline-primary w-100" onclick="showAnalytics()">
                            <i class="fas fa-chart-bar me-2"></i>View Analytics
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
function updateStatus(disputeId, status) {
    if (confirm('Are you sure you want to update this dispute status?')) {
        // In a real implementation, make AJAX call to update status
        console.log('Updating dispute', disputeId, 'to status', status);
        alert('Dispute status would be updated to: ' + status);
    }
}

function resolveDispute(disputeId) {
    const resolution = prompt('Please provide a resolution summary:');
    if (resolution) {
        // In a real implementation, make AJAX call to resolve with summary
        console.log('Resolving dispute', disputeId, 'with resolution:', resolution);
        alert('Dispute would be resolved with summary: ' + resolution);
    }
}

function escalateDispute(disputeId) {
    if (confirm('Are you sure you want to escalate this dispute to high priority?')) {
        // In a real implementation, make AJAX call to escalate
        console.log('Escalating dispute', disputeId);
        alert('Dispute would be escalated to high priority');
    }
}

function bulkInvestigate() {
    alert('Bulk investigate functionality would be implemented here');
}

function bulkResolve() {
    alert('Bulk resolve functionality would be implemented here');
}

function exportDisputes() {
    alert('Export disputes functionality would be implemented here');
}

function showAnalytics() {
    alert('Dispute analytics would be shown here');
}
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.layout', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp11\htdocs\bll\resources\views/admin/disputes/index.blade.php ENDPATH**/ ?>