<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class UserProfile extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'first_name',
        'last_name',
        'date_of_birth',
        'gender',
        'address',
        'postal_code',
        'national_id',
        'national_id_document',
        'identity_verified',
        'identity_verified_at',
        'website',
        'social_links',
        'skills',
        'experience',
        'timezone',
    ];

    protected function casts(): array
    {
        return [
            'date_of_birth' => 'date',
            'identity_verified' => 'boolean',
            'identity_verified_at' => 'datetime',
            'social_links' => 'array',
        ];
    }

    /**
     * Get the user that owns the profile
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get full name
     */
    public function getFullNameAttribute(): string
    {
        return trim($this->first_name . ' ' . $this->last_name);
    }

    /**
     * Check if identity is verified
     */
    public function isIdentityVerified(): bool
    {
        return $this->identity_verified;
    }
}
