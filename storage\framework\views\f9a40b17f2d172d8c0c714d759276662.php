<?php $__env->startSection('title', 'Seller Management'); ?>

<?php $__env->startSection('content'); ?>
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-store me-2"></i>Seller Management</h2>
    <div class="btn-group">
        <button type="button" class="btn btn-outline-primary active">All Sellers</button>
        <button type="button" class="btn btn-outline-warning">Pending</button>
        <button type="button" class="btn btn-outline-success">Approved</button>
        <button type="button" class="btn btn-outline-danger">Rejected</button>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card stat-card text-center">
            <div class="card-body">
                <div class="text-primary mb-2">
                    <i class="fas fa-store fa-2x"></i>
                </div>
                <h4 class="mb-0"><?php echo e(\App\Models\Seller::count()); ?></h4>
                <small class="text-muted">Total Sellers</small>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card stat-card text-center">
            <div class="card-body">
                <div class="text-success mb-2">
                    <i class="fas fa-check-circle fa-2x"></i>
                </div>
                <h4 class="mb-0"><?php echo e(\App\Models\Seller::where('verification_status', 'approved')->count()); ?></h4>
                <small class="text-muted">Approved</small>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card stat-card text-center">
            <div class="card-body">
                <div class="text-warning mb-2">
                    <i class="fas fa-clock fa-2x"></i>
                </div>
                <h4 class="mb-0"><?php echo e(\App\Models\Seller::where('verification_status', 'pending')->count()); ?></h4>
                <small class="text-muted">Pending</small>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card stat-card text-center">
            <div class="card-body">
                <div class="text-danger mb-2">
                    <i class="fas fa-times-circle fa-2x"></i>
                </div>
                <h4 class="mb-0"><?php echo e(\App\Models\Seller::where('verification_status', 'rejected')->count()); ?></h4>
                <small class="text-muted">Rejected</small>
            </div>
        </div>
    </div>
</div>

<!-- Sellers Table -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">Sellers List</h5>
    </div>
    <div class="card-body">
        <?php
            $sellers = \App\Models\Seller::with('user')->latest()->get();
        ?>
        
        <?php if($sellers->count() > 0): ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead class="table-light">
                        <tr>
                            <th>Seller</th>
                            <th>Business</th>
                            <th>Type</th>
                            <th>Status</th>
                            <th>Joined</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__currentLoopData = $sellers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $seller): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-circle me-3">
                                            <?php echo e(strtoupper(substr($seller->user->name, 0, 2))); ?>

                                        </div>
                                        <div>
                                            <div class="fw-bold"><?php echo e($seller->user->name); ?></div>
                                            <small class="text-muted"><?php echo e($seller->user->email); ?></small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div><?php echo e($seller->business_name); ?></div>
                                    <?php if($seller->business_address): ?>
                                        <small class="text-muted"><?php echo e(Str::limit($seller->business_address, 30)); ?></small>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <span class="badge bg-info"><?php echo e(ucfirst($seller->business_type)); ?></span>
                                </td>
                                <td>
                                    <span class="badge bg-<?php echo e($seller->verification_status === 'approved' ? 'success' : ($seller->verification_status === 'rejected' ? 'danger' : 'warning')); ?>">
                                        <?php echo e(ucfirst($seller->verification_status)); ?>

                                    </span>
                                    <?php if($seller->is_active): ?>
                                        <span class="badge bg-success ms-1">Active</span>
                                    <?php else: ?>
                                        <span class="badge bg-secondary ms-1">Inactive</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <small><?php echo e($seller->created_at->format('M j, Y')); ?></small>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-primary" title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <?php if($seller->verification_status === 'pending'): ?>
                                            <button class="btn btn-outline-success" title="Approve">
                                                <i class="fas fa-check"></i>
                                            </button>
                                            <button class="btn btn-outline-danger" title="Reject">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </tbody>
                </table>
            </div>
        <?php else: ?>
            <div class="text-center py-5">
                <i class="fas fa-store fa-3x text-muted mb-3"></i>
                <h5>No sellers found</h5>
                <p class="text-muted">Sellers will appear here once they register and complete their profiles.</p>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Coming Soon Notice -->
<div class="alert alert-info mt-4">
    <i class="fas fa-info-circle me-2"></i>
    <strong>Coming Soon:</strong> Full seller management features including document verification, approval workflow, and detailed seller profiles will be available in the next update.
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
    .avatar-circle {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        font-size: 14px;
    }
</style>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.layout', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp11\htdocs\bll\resources\views/admin/sellers/index.blade.php ENDPATH**/ ?>