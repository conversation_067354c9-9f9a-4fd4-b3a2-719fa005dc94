<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Seller Dashboard - Marketplace</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
        }
        .sidebar {
            background: linear-gradient(135deg, #007bff 0%, #6610f2 100%);
            min-height: 100vh;
            color: white;
        }
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            border-radius: 8px;
            margin: 2px 0;
        }
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            background-color: rgba(255, 255, 255, 0.1);
            color: white;
        }
        .stat-card {
            border-radius: 15px;
            border: none;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            transition: transform 0.3s ease;
        }
        .stat-card:hover {
            transform: translateY(-5px);
        }
        .verification-banner {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
            border-radius: 15px;
            color: white;
        }
        .verification-banner.verified {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 px-0">
                <div class="sidebar p-3">
                    <div class="text-center mb-4">
                        <h4><i class="fas fa-store me-2"></i>Seller Panel</h4>
                        <small>Welcome, {{ auth()->user()->name }}</small>
                    </div>
                    
                    <nav class="nav flex-column">
                        <a class="nav-link active" href="{{ route('seller.dashboard') }}">
                            <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                        </a>
                        <a class="nav-link" href="#">
                            <i class="fas fa-box me-2"></i>My Products
                        </a>
                        <a class="nav-link" href="#">
                            <i class="fas fa-shopping-cart me-2"></i>Orders
                        </a>
                        <a class="nav-link" href="#">
                            <i class="fas fa-chart-line me-2"></i>Analytics
                        </a>
                        <a class="nav-link" href="#">
                            <i class="fas fa-dollar-sign me-2"></i>Earnings
                        </a>
                        <a class="nav-link" href="#">
                            <i class="fas fa-star me-2"></i>Reviews
                        </a>
                        <a class="nav-link" href="#">
                            <i class="fas fa-user-edit me-2"></i>Profile
                        </a>
                        @if($seller && $seller->verification_status !== 'approved')
                            <a class="nav-link text-warning" href="{{ route('seller.verification') }}">
                                <i class="fas fa-exclamation-triangle me-2"></i>Verification
                            </a>
                        @endif
                        
                        <hr class="my-3">
                        
                        <form method="POST" action="{{ route('logout') }}">
                            @csrf
                            <button type="submit" class="nav-link btn btn-link text-start w-100 text-light">
                                <i class="fas fa-sign-out-alt me-2"></i>Logout
                            </button>
                        </form>
                    </nav>
                </div>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10">
                <div class="p-4">
                    <!-- Header -->
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2>Seller Dashboard</h2>
                        <div class="text-muted">
                            <i class="fas fa-calendar me-1"></i>{{ now()->format('F j, Y') }}
                        </div>
                    </div>
                    
                    <!-- Verification Status Banner -->
                    @if($seller)
                        <div class="verification-banner {{ $seller->verification_status === 'approved' ? 'verified' : '' }} p-4 mb-4">
                            <div class="row align-items-center">
                                <div class="col-md-8">
                                    @if($seller->verification_status === 'approved')
                                        <h5><i class="fas fa-check-circle me-2"></i>Account Verified</h5>
                                        <p class="mb-0">Your seller account is verified and active. You can now sell products and services.</p>
                                    @elseif($seller->verification_status === 'pending')
                                        <h5><i class="fas fa-clock me-2"></i>Verification Pending</h5>
                                        <p class="mb-0">Your account is under review. We'll notify you once verification is complete.</p>
                                    @else
                                        <h5><i class="fas fa-exclamation-triangle me-2"></i>Verification Required</h5>
                                        <p class="mb-0">Complete your verification to start selling on our marketplace.</p>
                                    @endif
                                </div>
                                <div class="col-md-4 text-end">
                                    @if($seller->verification_status !== 'approved')
                                        <a href="{{ route('seller.verification') }}" class="btn btn-light">
                                            <i class="fas fa-arrow-right me-2"></i>Complete Verification
                                        </a>
                                    @endif
                                </div>
                            </div>
                        </div>
                    @endif
                    
                    <!-- Statistics Cards -->
                    <div class="row mb-4">
                        <div class="col-md-3 mb-3">
                            <div class="card stat-card h-100">
                                <div class="card-body text-center">
                                    <div class="text-primary mb-2">
                                        <i class="fas fa-box fa-2x"></i>
                                    </div>
                                    <h4 class="mb-0">{{ $stats['total_products'] ?? 0 }}</h4>
                                    <small class="text-muted">Total Products</small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-3 mb-3">
                            <div class="card stat-card h-100">
                                <div class="card-body text-center">
                                    <div class="text-success mb-2">
                                        <i class="fas fa-shopping-cart fa-2x"></i>
                                    </div>
                                    <h4 class="mb-0">{{ $stats['total_orders'] ?? 0 }}</h4>
                                    <small class="text-muted">Total Orders</small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-3 mb-3">
                            <div class="card stat-card h-100">
                                <div class="card-body text-center">
                                    <div class="text-warning mb-2">
                                        <i class="fas fa-dollar-sign fa-2x"></i>
                                    </div>
                                    <h4 class="mb-0">${{ number_format($stats['total_earnings'] ?? 0, 2) }}</h4>
                                    <small class="text-muted">Total Earnings</small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-3 mb-3">
                            <div class="card stat-card h-100">
                                <div class="card-body text-center">
                                    <div class="text-info mb-2">
                                        <i class="fas fa-star fa-2x"></i>
                                    </div>
                                    <h4 class="mb-0">{{ number_format($stats['average_rating'] ?? 0, 1) }}</h4>
                                    <small class="text-muted">Average Rating</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Quick Actions -->
                    @if($seller && $seller->verification_status === 'approved')
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="mb-0"><i class="fas fa-bolt me-2"></i>Quick Actions</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-3 mb-2">
                                                <a href="#" class="btn btn-primary w-100">
                                                    <i class="fas fa-plus me-2"></i>Add Product
                                                </a>
                                            </div>
                                            <div class="col-md-3 mb-2">
                                                <a href="#" class="btn btn-success w-100">
                                                    <i class="fas fa-eye me-2"></i>View Orders
                                                </a>
                                            </div>
                                            <div class="col-md-3 mb-2">
                                                <a href="#" class="btn btn-info w-100">
                                                    <i class="fas fa-chart-bar me-2"></i>Analytics
                                                </a>
                                            </div>
                                            <div class="col-md-3 mb-2">
                                                <a href="#" class="btn btn-warning w-100">
                                                    <i class="fas fa-user-edit me-2"></i>Edit Profile
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endif
                    
                    <!-- Recent Activity -->
                    <div class="row">
                        <div class="col-md-8">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0"><i class="fas fa-clock me-2"></i>Recent Activity</h5>
                                </div>
                                <div class="card-body">
                                    @if(isset($recent_activities) && count($recent_activities) > 0)
                                        @foreach($recent_activities as $activity)
                                            <div class="d-flex align-items-center mb-3">
                                                <div class="me-3">
                                                    <i class="fas fa-circle text-primary" style="font-size: 0.5rem;"></i>
                                                </div>
                                                <div>
                                                    <div>{{ $activity['message'] }}</div>
                                                    <small class="text-muted">{{ $activity['time'] }}</small>
                                                </div>
                                            </div>
                                        @endforeach
                                    @else
                                        <div class="text-center text-muted py-4">
                                            <i class="fas fa-inbox fa-2x mb-3"></i>
                                            <p>No recent activity to display</p>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0"><i class="fas fa-bell me-2"></i>Notifications</h5>
                                </div>
                                <div class="card-body">
                                    @if(isset($notifications) && count($notifications) > 0)
                                        @foreach($notifications as $notification)
                                            <div class="alert alert-{{ $notification['type'] }} alert-sm mb-2">
                                                <small>{{ $notification['message'] }}</small>
                                            </div>
                                        @endforeach
                                    @else
                                        <div class="text-center text-muted py-4">
                                            <i class="fas fa-bell-slash fa-2x mb-3"></i>
                                            <p>No new notifications</p>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
