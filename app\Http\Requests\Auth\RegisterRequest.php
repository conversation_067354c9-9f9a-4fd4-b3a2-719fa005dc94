<?php

namespace App\Http\Requests\Auth;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Password;

class RegisterRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $rules = [
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users'],
            'phone' => ['nullable', 'string', 'max:20'],
            'password' => ['required', 'confirmed', Password::defaults()],
            'first_name' => ['required', 'string', 'max:255'],
            'last_name' => ['required', 'string', 'max:255'],
            'country' => ['nullable', 'string', 'max:255'],
            'city' => ['nullable', 'string', 'max:255'],
            'language' => ['nullable', 'string', 'in:en,ar'],
            'timezone' => ['nullable', 'string', 'max:255'],
            'terms' => ['required', 'accepted'],
        ];

        // Additional rules for seller registration
        if ($this->routeIs('register.seller')) {
            $rules = array_merge($rules, [
                'business_name' => ['required', 'string', 'max:255'],
                'business_type' => ['required', 'string', 'in:individual,company,partnership'],
                'business_address' => ['nullable', 'string', 'max:500'],
                'skills' => ['nullable', 'string', 'max:1000'],
                'experience' => ['nullable', 'string', 'max:2000'],
                'website' => ['nullable', 'url', 'max:255'],
            ]);
        }

        return $rules;
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'name.required' => 'The name field is required.',
            'email.required' => 'The email field is required.',
            'email.email' => 'Please enter a valid email address.',
            'email.unique' => 'This email address is already registered.',
            'password.required' => 'The password field is required.',
            'password.confirmed' => 'The password confirmation does not match.',
            'first_name.required' => 'The first name field is required.',
            'last_name.required' => 'The last name field is required.',
            'terms.required' => 'You must accept the terms and conditions.',
            'terms.accepted' => 'You must accept the terms and conditions.',
            'business_name.required' => 'The business name field is required for sellers.',
            'business_type.required' => 'Please select your business type.',
            'business_type.in' => 'Please select a valid business type.',
            'website.url' => 'Please enter a valid website URL.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'first_name' => 'first name',
            'last_name' => 'last name',
            'business_name' => 'business name',
            'business_type' => 'business type',
            'business_address' => 'business address',
        ];
    }
}
