@extends('admin.layout')

@section('title', 'Seller Management')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-store me-2"></i>Seller Management</h2>
    <div class="btn-group">
        <button type="button" class="btn btn-outline-primary active">All Sellers</button>
        <button type="button" class="btn btn-outline-warning">Pending</button>
        <button type="button" class="btn btn-outline-success">Approved</button>
        <button type="button" class="btn btn-outline-danger">Rejected</button>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card stat-card text-center">
            <div class="card-body">
                <div class="text-primary mb-2">
                    <i class="fas fa-store fa-2x"></i>
                </div>
                <h4 class="mb-0">{{ \App\Models\Seller::count() }}</h4>
                <small class="text-muted">Total Sellers</small>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card stat-card text-center">
            <div class="card-body">
                <div class="text-success mb-2">
                    <i class="fas fa-check-circle fa-2x"></i>
                </div>
                <h4 class="mb-0">{{ \App\Models\Seller::where('verification_status', 'approved')->count() }}</h4>
                <small class="text-muted">Approved</small>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card stat-card text-center">
            <div class="card-body">
                <div class="text-warning mb-2">
                    <i class="fas fa-clock fa-2x"></i>
                </div>
                <h4 class="mb-0">{{ \App\Models\Seller::where('verification_status', 'pending')->count() }}</h4>
                <small class="text-muted">Pending</small>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card stat-card text-center">
            <div class="card-body">
                <div class="text-danger mb-2">
                    <i class="fas fa-times-circle fa-2x"></i>
                </div>
                <h4 class="mb-0">{{ \App\Models\Seller::where('verification_status', 'rejected')->count() }}</h4>
                <small class="text-muted">Rejected</small>
            </div>
        </div>
    </div>
</div>

<!-- Sellers Table -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">Sellers List</h5>
    </div>
    <div class="card-body">
        @php
            $sellers = \App\Models\Seller::with('user')->latest()->get();
        @endphp
        
        @if($sellers->count() > 0)
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead class="table-light">
                        <tr>
                            <th>Seller</th>
                            <th>Business</th>
                            <th>Type</th>
                            <th>Status</th>
                            <th>Joined</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($sellers as $seller)
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-circle me-3">
                                            {{ strtoupper(substr($seller->user->name, 0, 2)) }}
                                        </div>
                                        <div>
                                            <div class="fw-bold">{{ $seller->user->name }}</div>
                                            <small class="text-muted">{{ $seller->user->email }}</small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div>{{ $seller->business_name }}</div>
                                    @if($seller->business_address)
                                        <small class="text-muted">{{ Str::limit($seller->business_address, 30) }}</small>
                                    @endif
                                </td>
                                <td>
                                    <span class="badge bg-info">{{ ucfirst($seller->business_type) }}</span>
                                </td>
                                <td>
                                    <span class="badge bg-{{ $seller->verification_status === 'approved' ? 'success' : ($seller->verification_status === 'rejected' ? 'danger' : 'warning') }}">
                                        {{ ucfirst($seller->verification_status) }}
                                    </span>
                                    @if($seller->is_active)
                                        <span class="badge bg-success ms-1">Active</span>
                                    @else
                                        <span class="badge bg-secondary ms-1">Inactive</span>
                                    @endif
                                </td>
                                <td>
                                    <small>{{ $seller->created_at->format('M j, Y') }}</small>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-primary" title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        @if($seller->verification_status === 'pending')
                                            <button class="btn btn-outline-success" title="Approve">
                                                <i class="fas fa-check"></i>
                                            </button>
                                            <button class="btn btn-outline-danger" title="Reject">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        @endif
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        @else
            <div class="text-center py-5">
                <i class="fas fa-store fa-3x text-muted mb-3"></i>
                <h5>No sellers found</h5>
                <p class="text-muted">Sellers will appear here once they register and complete their profiles.</p>
            </div>
        @endif
    </div>
</div>

<!-- Coming Soon Notice -->
<div class="alert alert-info mt-4">
    <i class="fas fa-info-circle me-2"></i>
    <strong>Coming Soon:</strong> Full seller management features including document verification, approval workflow, and detailed seller profiles will be available in the next update.
</div>
@endsection

@push('styles')
<style>
    .avatar-circle {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        font-size: 14px;
    }
</style>
@endpush
