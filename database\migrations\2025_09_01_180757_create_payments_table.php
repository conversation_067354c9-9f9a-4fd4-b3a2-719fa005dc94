<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payments', function (Blueprint $table) {
            $table->id();
            $table->string('payment_id')->unique(); // External payment gateway ID
            $table->foreignId('order_id')->constrained()->onDelete('restrict');
            $table->foreignId('buyer_id')->constrained('users')->onDelete('restrict');
            $table->foreignId('seller_id')->constrained('users')->onDelete('restrict');
            $table->decimal('amount', 10, 2);
            $table->decimal('platform_fee', 10, 2);
            $table->decimal('seller_amount', 10, 2);
            $table->string('currency', 3)->default('USD');
            $table->enum('payment_method', ['stripe', 'paypal', 'bank_transfer', 'wallet'])->default('stripe');
            $table->enum('status', [
                'pending',
                'processing',
                'completed',
                'failed',
                'cancelled',
                'refunded',
                'held_in_escrow',
                'released_to_seller'
            ])->default('pending');
            $table->json('gateway_response')->nullable(); // Payment gateway response
            $table->string('transaction_id')->nullable(); // Gateway transaction ID
            $table->timestamp('paid_at')->nullable();
            $table->timestamp('released_at')->nullable(); // When funds released to seller
            $table->timestamp('refunded_at')->nullable();
            $table->text('failure_reason')->nullable();
            $table->text('notes')->nullable();
            $table->boolean('is_escrow')->default(true); // Funds held in escrow
            $table->integer('escrow_release_days')->default(3); // Days to auto-release
            $table->timestamp('escrow_release_date')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payments');
    }
};
