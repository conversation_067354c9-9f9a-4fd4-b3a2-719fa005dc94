<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('disputes', function (Blueprint $table) {
            $table->id();
            $table->string('dispute_number')->unique();
            $table->foreignId('order_id')->constrained()->onDelete('restrict');
            $table->foreignId('raised_by')->constrained('users')->onDelete('restrict'); // Who raised the dispute
            $table->foreignId('against_user')->constrained('users')->onDelete('restrict'); // Against whom
            $table->enum('type', [
                'not_delivered',
                'poor_quality',
                'not_as_described',
                'late_delivery',
                'communication_issues',
                'payment_issues',
                'other'
            ]);
            $table->string('subject');
            $table->text('description');
            $table->json('evidence')->nullable(); // Files, screenshots, etc.
            $table->enum('status', [
                'open',
                'under_review',
                'awaiting_response',
                'resolved',
                'closed'
            ])->default('open');
            $table->enum('resolution', [
                'refund_buyer',
                'release_to_seller',
                'partial_refund',
                'order_cancellation',
                'extend_delivery',
                'other'
            ])->nullable();
            $table->text('resolution_details')->nullable();
            $table->decimal('refund_amount', 10, 2)->nullable();
            $table->foreignId('assigned_to')->nullable()->constrained('users'); // Admin handling the dispute
            $table->timestamp('resolved_at')->nullable();
            $table->text('admin_notes')->nullable();
            $table->json('timeline')->nullable(); // Dispute resolution timeline
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('disputes');
    }
};
