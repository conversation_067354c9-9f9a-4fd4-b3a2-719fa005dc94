<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Support\Facades\Auth;

class DashboardController extends Controller
{
    /**
     * Admin Dashboard
     */
    public function adminDashboard(): View
    {
        $user = Auth::user();

        // Get admin dashboard statistics
        $stats = [
            'total_users' => \App\Models\User::count(),
            'total_sellers' => \App\Models\Seller::count(),
            'pending_sellers' => \App\Models\Seller::where('verification_status', 'pending')->count(),
            'total_orders' => 0, // Will be implemented when Order model is created
        ];

        // Get recent user registrations
        $recent_users = \App\Models\User::latest()->take(5)->get();
        $recent_activities = [];

        foreach ($recent_users as $recentUser) {
            $recent_activities[] = [
                'message' => "New {$recentUser->role} registered: {$recentUser->name}",
                'time' => $recentUser->created_at->diffForHumans()
            ];
        }

        // Get pending sellers for admin action
        $pending_sellers_count = \App\Models\Seller::where('verification_status', 'pending')->count();
        $pending_actions = [];

        if ($pending_sellers_count > 0) {
            $pending_actions[] = [
                'type' => 'warning',
                'message' => "{$pending_sellers_count} sellers pending verification"
            ];
        }

        return view('admin.dashboard', compact('user', 'stats', 'recent_activities', 'pending_actions'));
    }

    /**
     * Analytics Dashboard
     */
    public function analytics(): View
    {
        // Get user distribution data
        $userStats = [
            'buyers' => \App\Models\User::where('role', 'buyer')->count(),
            'sellers' => \App\Models\User::where('role', 'seller')->count(),
            'admins' => \App\Models\User::where('role', 'admin')->count(),
        ];

        return view('admin.analytics', compact('userStats'));
    }

    /**
     * Seller Dashboard
     */
    public function sellerDashboard(): View
    {
        $user = Auth::user();
        $seller = $user->seller ?? null;

        // Get seller dashboard statistics (using mock data for now)
        $stats = [
            'total_products' => 12,
            'active_products' => 8,
            'total_orders' => 45,
            'pending_orders' => 3,
            'total_earnings' => 2450.75,
            'pending_earnings' => 320.50,
            'available_balance' => 2130.25,
            'average_rating' => 4.7,
        ];

        // Recent orders (mock data)
        $recentOrders = collect([
            (object) [
                'id' => 1,
                'order_number' => 'ORD-2024-001',
                'buyer_name' => 'John Doe',
                'product_name' => 'Wireless Headphones',
                'amount' => 199.99,
                'status' => 'completed',
                'created_at' => now()->subDays(1)
            ],
            (object) [
                'id' => 2,
                'order_number' => 'ORD-2024-002',
                'buyer_name' => 'Jane Smith',
                'product_name' => 'Phone Case',
                'amount' => 29.99,
                'status' => 'pending',
                'created_at' => now()->subHours(6)
            ]
        ]);

        return view('seller.dashboard', compact('user', 'seller', 'stats', 'recentOrders'));
    }

    /**
     * Buyer Dashboard
     */
    public function buyerDashboard(): View
    {
        $user = Auth::user();

        // Get buyer dashboard statistics (using mock data for now)
        $stats = [
            'total_orders' => 8,
            'active_orders' => 2,
            'completed_orders' => 6,
            'total_spent' => 1250.75,
        ];

        // Recent orders (mock data)
        $recentOrders = collect([
            (object) [
                'id' => 1,
                'order_number' => 'ORD-2024-001',
                'seller_name' => 'Tech Store',
                'product_name' => 'Wireless Headphones',
                'amount' => 199.99,
                'status' => 'delivered',
                'created_at' => now()->subDays(3)
            ],
            (object) [
                'id' => 2,
                'order_number' => 'ORD-2024-002',
                'seller_name' => 'Fashion Hub',
                'product_name' => 'Designer T-Shirt',
                'amount' => 49.99,
                'status' => 'shipped',
                'created_at' => now()->subDays(1)
            ]
        ]);

        // Recommended products (mock data)
        $recommendedProducts = collect([
            (object) [
                'id' => 1,
                'name' => 'Smart Watch',
                'price' => 299.99,
                'image' => 'https://via.placeholder.com/200x200?text=Smart+Watch',
                'seller_name' => 'Tech Store'
            ],
            (object) [
                'id' => 2,
                'name' => 'Bluetooth Speaker',
                'price' => 79.99,
                'image' => 'https://via.placeholder.com/200x200?text=Speaker',
                'seller_name' => 'Audio Plus'
            ]
        ]);

        return view('buyer.dashboard', compact('user', 'stats', 'recentOrders', 'recommendedProducts'));
    }
}
