<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\View\View;

class OrderController extends Controller
{
    /**
     * Display a listing of orders
     */
    public function index(Request $request): View
    {
        // For now, we'll use mock data since Order model doesn't exist yet
        $orders = collect([
            (object) [
                'id' => 1,
                'order_number' => 'ORD-2024-001',
                'buyer_name' => '<PERSON>',
                'seller_name' => 'Tech Store',
                'total_amount' => 299.99,
                'status' => 'pending',
                'created_at' => now()->subDays(2),
                'items_count' => 3
            ],
            (object) [
                'id' => 2,
                'order_number' => 'ORD-2024-002',
                'buyer_name' => '<PERSON>',
                'seller_name' => 'Fashion Hub',
                'total_amount' => 149.50,
                'status' => 'processing',
                'created_at' => now()->subDays(1),
                'items_count' => 2
            ],
            (object) [
                'id' => 3,
                'order_number' => 'ORD-2024-003',
                'buyer_name' => '<PERSON>',
                'seller_name' => 'Electronics Plus',
                'total_amount' => 599.00,
                'status' => 'shipped',
                'created_at' => now()->subHours(12),
                'items_count' => 1
            ],
            (object) [
                'id' => 4,
                'order_number' => 'ORD-2024-004',
                'buyer_name' => 'Sarah Wilson',
                'seller_name' => 'Home Goods',
                'total_amount' => 89.99,
                'status' => 'delivered',
                'created_at' => now()->subHours(6),
                'items_count' => 4
            ]
        ]);

        // Filter by status if provided
        if ($request->filled('status')) {
            $orders = $orders->where('status', $request->status);
        }

        // Search functionality
        if ($request->filled('search')) {
            $search = strtolower($request->search);
            $orders = $orders->filter(function($order) use ($search) {
                return str_contains(strtolower($order->order_number), $search) ||
                       str_contains(strtolower($order->buyer_name), $search) ||
                       str_contains(strtolower($order->seller_name), $search);
            });
        }

        $stats = [
            'total_orders' => 4,
            'pending_orders' => 1,
            'processing_orders' => 1,
            'shipped_orders' => 1,
            'delivered_orders' => 1,
            'total_revenue' => 1138.48
        ];

        return view('admin.orders.index', compact('orders', 'stats'));
    }

    /**
     * Display the specified order
     */
    public function show($id): View
    {
        // Mock order data
        $order = (object) [
            'id' => $id,
            'order_number' => 'ORD-2024-00' . $id,
            'buyer_name' => 'John Doe',
            'buyer_email' => '<EMAIL>',
            'seller_name' => 'Tech Store',
            'seller_email' => '<EMAIL>',
            'total_amount' => 299.99,
            'status' => 'pending',
            'created_at' => now()->subDays(2),
            'shipping_address' => '123 Main St, City, State 12345',
            'payment_method' => 'Credit Card',
            'items' => [
                (object) ['name' => 'Wireless Headphones', 'quantity' => 1, 'price' => 199.99],
                (object) ['name' => 'Phone Case', 'quantity' => 2, 'price' => 50.00]
            ]
        ];

        return view('admin.orders.show', compact('order'));
    }

    /**
     * Update order status
     */
    public function updateStatus(Request $request, $id)
    {
        $request->validate([
            'status' => 'required|in:pending,processing,shipped,delivered,cancelled,refunded'
        ]);

        // In a real implementation, you would update the order in the database
        // Order::findOrFail($id)->update(['status' => $request->status]);

        return redirect()->route('admin.orders.show', $id)
                        ->with('success', 'Order status updated successfully.');
    }
}
