<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\View\View;

class DisputeController extends Controller
{
    /**
     * Display a listing of disputes
     */
    public function index(Request $request): View
    {
        // Mock dispute data
        $disputes = collect([
            (object) [
                'id' => 1,
                'dispute_number' => 'DSP-2024-001',
                'order_number' => 'ORD-2024-001',
                'buyer_name' => '<PERSON>',
                'seller_name' => 'Tech Store',
                'subject' => 'Product not as described',
                'status' => 'open',
                'priority' => 'high',
                'amount' => 199.99,
                'created_at' => now()->subDays(2),
                'last_activity' => now()->subHours(6)
            ],
            (object) [
                'id' => 2,
                'dispute_number' => 'DSP-2024-002',
                'order_number' => 'ORD-2024-002',
                'buyer_name' => '<PERSON>',
                'seller_name' => 'Fashion Hub',
                'subject' => 'Item never received',
                'status' => 'investigating',
                'priority' => 'medium',
                'amount' => 149.50,
                'created_at' => now()->subDays(1),
                'last_activity' => now()->subHours(2)
            ],
            (object) [
                'id' => 3,
                'dispute_number' => 'DSP-2024-003',
                'order_number' => 'ORD-2024-003',
                'buyer_name' => 'Mike Johnson',
                'seller_name' => 'Electronics Plus',
                'subject' => 'Damaged during shipping',
                'status' => 'resolved',
                'priority' => 'low',
                'amount' => 599.00,
                'created_at' => now()->subDays(5),
                'last_activity' => now()->subDays(1)
            ]
        ]);

        // Filter by status
        if ($request->filled('status')) {
            $disputes = $disputes->where('status', $request->status);
        }

        // Filter by priority
        if ($request->filled('priority')) {
            $disputes = $disputes->where('priority', $request->priority);
        }

        // Search functionality
        if ($request->filled('search')) {
            $search = strtolower($request->search);
            $disputes = $disputes->filter(function($dispute) use ($search) {
                return str_contains(strtolower($dispute->dispute_number), $search) ||
                       str_contains(strtolower($dispute->buyer_name), $search) ||
                       str_contains(strtolower($dispute->seller_name), $search) ||
                       str_contains(strtolower($dispute->subject), $search);
            });
        }

        $stats = [
            'total_disputes' => 3,
            'open_disputes' => 1,
            'investigating_disputes' => 1,
            'resolved_disputes' => 1,
            'high_priority' => 1,
            'avg_resolution_time' => '2.5 days'
        ];

        return view('admin.disputes.index', compact('disputes', 'stats'));
    }

    /**
     * Display the specified dispute
     */
    public function show($id): View
    {
        // Mock dispute data
        $dispute = (object) [
            'id' => $id,
            'dispute_number' => 'DSP-2024-00' . $id,
            'order_number' => 'ORD-2024-001',
            'buyer_name' => 'John Doe',
            'buyer_email' => '<EMAIL>',
            'seller_name' => 'Tech Store',
            'seller_email' => '<EMAIL>',
            'subject' => 'Product not as described',
            'description' => 'The wireless headphones I received do not match the description. They are missing the noise cancellation feature that was advertised.',
            'status' => 'open',
            'priority' => 'high',
            'amount' => 199.99,
            'created_at' => now()->subDays(2),
            'evidence' => [
                'Product photos showing missing features',
                'Original product listing screenshot',
                'Email correspondence with seller'
            ],
            'messages' => [
                (object) [
                    'sender' => 'John Doe (Buyer)',
                    'message' => 'The product I received is not as described. Missing noise cancellation.',
                    'timestamp' => now()->subDays(2)
                ],
                (object) [
                    'sender' => 'Tech Store (Seller)',
                    'message' => 'We apologize for the confusion. This is an older model without that feature.',
                    'timestamp' => now()->subDays(1)
                ],
                (object) [
                    'sender' => 'Admin',
                    'message' => 'We are investigating this dispute. Please provide additional evidence.',
                    'timestamp' => now()->subHours(6)
                ]
            ]
        ];

        return view('admin.disputes.show', compact('dispute'));
    }

    /**
     * Update dispute status
     */
    public function updateStatus(Request $request, $id)
    {
        $request->validate([
            'status' => 'required|in:open,investigating,resolved,closed',
            'resolution_note' => 'nullable|string|max:1000'
        ]);

        // In a real implementation, you would update the dispute in the database
        // Dispute::findOrFail($id)->update(['status' => $request->status, 'resolution_note' => $request->resolution_note]);

        return redirect()->route('admin.disputes.show', $id)
                        ->with('success', 'Dispute status updated successfully.');
    }

    /**
     * Add message to dispute
     */
    public function addMessage(Request $request, $id)
    {
        $request->validate([
            'message' => 'required|string|max:1000'
        ]);

        // In a real implementation, you would add the message to the database
        // DisputeMessage::create(['dispute_id' => $id, 'sender' => 'Admin', 'message' => $request->message]);

        return redirect()->route('admin.disputes.show', $id)
                        ->with('success', 'Message added successfully.');
    }
}
