<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\View\View;

class ProductController extends Controller
{
    /**
     * Display a listing of products
     */
    public function index(Request $request): View
    {
        // Mock product data
        $products = collect([
            (object) [
                'id' => 1,
                'name' => 'Wireless Bluetooth Headphones',
                'seller_name' => 'Tech Store',
                'category' => 'Electronics',
                'price' => 199.99,
                'status' => 'active',
                'stock' => 25,
                'created_at' => now()->subDays(5),
                'image' => 'https://via.placeholder.com/100x100?text=Headphones'
            ],
            (object) [
                'id' => 2,
                'name' => 'Premium Phone Case',
                'seller_name' => 'Mobile Accessories',
                'category' => 'Accessories',
                'price' => 29.99,
                'status' => 'pending',
                'stock' => 50,
                'created_at' => now()->subDays(3),
                'image' => 'https://via.placeholder.com/100x100?text=Case'
            ],
            (object) [
                'id' => 3,
                'name' => 'Smart Watch Series 5',
                'seller_name' => 'Wearable Tech',
                'category' => 'Electronics',
                'price' => 399.99,
                'status' => 'active',
                'stock' => 15,
                'created_at' => now()->subDays(1),
                'image' => 'https://via.placeholder.com/100x100?text=Watch'
            ],
            (object) [
                'id' => 4,
                'name' => 'Organic Cotton T-Shirt',
                'seller_name' => 'Fashion Hub',
                'category' => 'Clothing',
                'price' => 24.99,
                'status' => 'inactive',
                'stock' => 0,
                'created_at' => now()->subHours(12),
                'image' => 'https://via.placeholder.com/100x100?text=Shirt'
            ]
        ]);

        // Filter by status
        if ($request->filled('status')) {
            $products = $products->where('status', $request->status);
        }

        // Filter by category
        if ($request->filled('category')) {
            $products = $products->where('category', $request->category);
        }

        // Search functionality
        if ($request->filled('search')) {
            $search = strtolower($request->search);
            $products = $products->filter(function($product) use ($search) {
                return str_contains(strtolower($product->name), $search) ||
                       str_contains(strtolower($product->seller_name), $search) ||
                       str_contains(strtolower($product->category), $search);
            });
        }

        $stats = [
            'total_products' => 4,
            'active_products' => 2,
            'pending_products' => 1,
            'inactive_products' => 1,
            'low_stock_products' => 1
        ];

        $categories = ['Electronics', 'Accessories', 'Clothing', 'Home & Garden', 'Sports'];

        return view('admin.products.index', compact('products', 'stats', 'categories'));
    }

    /**
     * Display the specified product
     */
    public function show($id): View
    {
        // Mock product data
        $product = (object) [
            'id' => $id,
            'name' => 'Wireless Bluetooth Headphones',
            'description' => 'High-quality wireless headphones with noise cancellation and 30-hour battery life.',
            'seller_name' => 'Tech Store',
            'seller_email' => '<EMAIL>',
            'category' => 'Electronics',
            'price' => 199.99,
            'status' => 'active',
            'stock' => 25,
            'sku' => 'WBH-001',
            'weight' => '0.5 kg',
            'dimensions' => '20x15x8 cm',
            'created_at' => now()->subDays(5),
            'images' => [
                'https://via.placeholder.com/400x400?text=Headphones+1',
                'https://via.placeholder.com/400x400?text=Headphones+2',
                'https://via.placeholder.com/400x400?text=Headphones+3'
            ],
            'specifications' => [
                'Battery Life' => '30 hours',
                'Connectivity' => 'Bluetooth 5.0',
                'Noise Cancellation' => 'Active',
                'Weight' => '250g'
            ]
        ];

        return view('admin.products.show', compact('product'));
    }

    /**
     * Update product status
     */
    public function updateStatus(Request $request, $id)
    {
        $request->validate([
            'status' => 'required|in:active,pending,inactive,rejected'
        ]);

        // In a real implementation, you would update the product in the database
        // Product::findOrFail($id)->update(['status' => $request->status]);

        return redirect()->route('admin.products.show', $id)
                        ->with('success', 'Product status updated successfully.');
    }

    /**
     * Approve product
     */
    public function approve($id)
    {
        // In a real implementation, you would update the product status
        // Product::findOrFail($id)->update(['status' => 'active']);

        return redirect()->route('admin.products.index')
                        ->with('success', 'Product approved successfully.');
    }

    /**
     * Reject product
     */
    public function reject(Request $request, $id)
    {
        $request->validate([
            'reason' => 'required|string|max:500'
        ]);

        // In a real implementation, you would update the product and store rejection reason
        // Product::findOrFail($id)->update(['status' => 'rejected', 'rejection_reason' => $request->reason]);

        return redirect()->route('admin.products.index')
                        ->with('success', 'Product rejected successfully.');
    }
}
