<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\RegisterRequest;
use App\Models\User;
use App\Models\UserProfile;
use App\Models\Seller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;
use Illuminate\Auth\Events\Registered;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;

class RegisterController extends Controller
{
    /**
     * Show the registration form
     */
    public function showRegistrationForm(): View
    {
        return view('auth.register');
    }

    /**
     * Show the buyer registration form
     */
    public function showBuyerRegistrationForm(): View
    {
        return view('auth.register-buyer');
    }

    /**
     * Show the seller registration form
     */
    public function showSellerRegistrationForm(): View
    {
        return view('auth.register-seller');
    }

    /**
     * Handle buyer registration
     */
    public function registerBuyer(RegisterRequest $request): RedirectResponse|JsonResponse
    {
        try {
            DB::beginTransaction();

            $user = User::create([
                'name' => $request->name,
                'email' => $request->email,
                'phone' => $request->phone,
                'password' => Hash::make($request->password),
                'role' => 'buyer',
                'country' => $request->country,
                'city' => $request->city,
                'language' => $request->language ?? 'en',
            ]);

            // Create user profile
            UserProfile::create([
                'user_id' => $user->id,
                'first_name' => $request->first_name,
                'last_name' => $request->last_name,
                'timezone' => $request->timezone ?? 'UTC',
            ]);

            DB::commit();

            event(new Registered($user));

            auth()->login($user);

            if ($request->expectsJson()) {
                return response()->json([
                    'message' => 'Registration successful',
                    'user' => $user->load('profile'),
                    'redirect' => route('buyer.dashboard')
                ]);
            }

            return redirect()->route('buyer.dashboard')
                ->with('success', 'Registration successful! Welcome to our marketplace.');

        } catch (\Exception $e) {
            DB::rollBack();

            if ($request->expectsJson()) {
                return response()->json([
                    'message' => 'Registration failed',
                    'error' => $e->getMessage()
                ], 422);
            }

            return back()->withInput()
                ->withErrors(['error' => 'Registration failed. Please try again.']);
        }
    }

    /**
     * Handle seller registration
     */
    public function registerSeller(RegisterRequest $request): RedirectResponse|JsonResponse
    {
        try {
            DB::beginTransaction();

            $user = User::create([
                'name' => $request->name,
                'email' => $request->email,
                'phone' => $request->phone,
                'password' => Hash::make($request->password),
                'role' => 'seller',
                'country' => $request->country,
                'city' => $request->city,
                'language' => $request->language ?? 'en',
            ]);

            // Create user profile
            UserProfile::create([
                'user_id' => $user->id,
                'first_name' => $request->first_name,
                'last_name' => $request->last_name,
                'skills' => $request->skills,
                'experience' => $request->experience,
                'website' => $request->website,
                'timezone' => $request->timezone ?? 'UTC',
            ]);

            // Create seller profile
            Seller::create([
                'user_id' => $user->id,
                'business_name' => $request->business_name,
                'business_type' => $request->business_type ?? 'individual',
                'business_address' => $request->business_address,
                'verification_status' => 'pending',
                'is_active' => false, // Inactive until verified
            ]);

            DB::commit();

            event(new Registered($user));

            auth()->login($user);

            if ($request->expectsJson()) {
                return response()->json([
                    'message' => 'Registration successful. Your account is pending verification.',
                    'user' => $user->load(['profile', 'seller']),
                    'redirect' => route('seller.dashboard')
                ]);
            }

            return redirect()->route('seller.dashboard')
                ->with('info', 'Registration successful! Your seller account is pending verification. Please complete your profile and upload required documents.');

        } catch (\Exception $e) {
            DB::rollBack();

            if ($request->expectsJson()) {
                return response()->json([
                    'message' => 'Registration failed',
                    'error' => $e->getMessage()
                ], 422);
            }

            return back()->withInput()
                ->withErrors(['error' => 'Registration failed. Please try again.']);
        }
    }
}
