<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('orders', function (Blueprint $table) {
            $table->id();
            $table->string('order_number')->unique();
            $table->foreignId('buyer_id')->constrained('users')->onDelete('restrict');
            $table->foreignId('seller_id')->constrained('users')->onDelete('restrict');
            $table->foreignId('product_id')->constrained()->onDelete('restrict');
            $table->string('package_type')->default('basic'); // basic, standard, premium
            $table->decimal('amount', 10, 2);
            $table->decimal('platform_fee', 10, 2);
            $table->decimal('seller_amount', 10, 2);
            $table->string('currency', 3)->default('USD');
            $table->enum('status', [
                'pending_payment',
                'paid',
                'in_progress',
                'delivered',
                'revision_requested',
                'completed',
                'cancelled',
                'disputed'
            ])->default('pending_payment');
            $table->text('requirements')->nullable(); // Buyer requirements
            $table->json('deliverables')->nullable(); // Files delivered by seller
            $table->text('delivery_note')->nullable(); // Seller's delivery message
            $table->integer('delivery_time'); // Expected delivery in days
            $table->timestamp('delivery_date')->nullable();
            $table->timestamp('delivered_at')->nullable();
            $table->timestamp('completed_at')->nullable();
            $table->timestamp('cancelled_at')->nullable();
            $table->text('cancellation_reason')->nullable();
            $table->integer('revisions_used')->default(0);
            $table->integer('revisions_allowed')->default(1);
            $table->boolean('auto_complete')->default(true); // Auto-complete after X days
            $table->integer('auto_complete_days')->default(3);
            $table->json('timeline')->nullable(); // Order status timeline
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('orders');
    }
};
