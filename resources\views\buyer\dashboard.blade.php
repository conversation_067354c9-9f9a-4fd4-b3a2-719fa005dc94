<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Buyer Dashboard - Marketplace</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
        }
        .sidebar {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            min-height: 100vh;
            color: white;
        }
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            border-radius: 8px;
            margin: 2px 0;
        }
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            background-color: rgba(255, 255, 255, 0.1);
            color: white;
        }
        .stat-card {
            border-radius: 15px;
            border: none;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            transition: transform 0.3s ease;
        }
        .stat-card:hover {
            transform: translateY(-5px);
        }
        .product-card {
            border-radius: 10px;
            border: none;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }
        .product-card:hover {
            transform: translateY(-3px);
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 px-0">
                <div class="sidebar p-3">
                    <div class="text-center mb-4">
                        <h4><i class="fas fa-shopping-cart me-2"></i>Buyer Panel</h4>
                        <small>Welcome, {{ auth()->user()->name }}</small>
                    </div>
                    
                    <nav class="nav flex-column">
                        <a class="nav-link active" href="{{ route('buyer.dashboard') }}">
                            <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                        </a>
                        <a class="nav-link" href="#">
                            <i class="fas fa-search me-2"></i>Browse Products
                        </a>
                        <a class="nav-link" href="#">
                            <i class="fas fa-shopping-bag me-2"></i>My Orders
                        </a>
                        <a class="nav-link" href="#">
                            <i class="fas fa-heart me-2"></i>Wishlist
                        </a>
                        <a class="nav-link" href="#">
                            <i class="fas fa-star me-2"></i>Reviews
                        </a>
                        <a class="nav-link" href="#">
                            <i class="fas fa-credit-card me-2"></i>Payment Methods
                        </a>
                        <a class="nav-link" href="#">
                            <i class="fas fa-map-marker-alt me-2"></i>Addresses
                        </a>
                        <a class="nav-link" href="#">
                            <i class="fas fa-user-edit me-2"></i>Profile
                        </a>
                        
                        <hr class="my-3">
                        
                        <form method="POST" action="{{ route('logout') }}">
                            @csrf
                            <button type="submit" class="nav-link btn btn-link text-start w-100 text-light">
                                <i class="fas fa-sign-out-alt me-2"></i>Logout
                            </button>
                        </form>
                    </nav>
                </div>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10">
                <div class="p-4">
                    <!-- Header -->
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2>Buyer Dashboard</h2>
                        <div class="text-muted">
                            <i class="fas fa-calendar me-1"></i>{{ now()->format('F j, Y') }}
                        </div>
                    </div>
                    
                    <!-- Statistics Cards -->
                    <div class="row mb-4">
                        <div class="col-md-3 mb-3">
                            <div class="card stat-card h-100">
                                <div class="card-body text-center">
                                    <div class="text-primary mb-2">
                                        <i class="fas fa-shopping-bag fa-2x"></i>
                                    </div>
                                    <h4 class="mb-0">{{ $stats['total_orders'] ?? 0 }}</h4>
                                    <small class="text-muted">Total Orders</small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-3 mb-3">
                            <div class="card stat-card h-100">
                                <div class="card-body text-center">
                                    <div class="text-warning mb-2">
                                        <i class="fas fa-clock fa-2x"></i>
                                    </div>
                                    <h4 class="mb-0">{{ $stats['pending_orders'] ?? 0 }}</h4>
                                    <small class="text-muted">Pending Orders</small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-3 mb-3">
                            <div class="card stat-card h-100">
                                <div class="card-body text-center">
                                    <div class="text-success mb-2">
                                        <i class="fas fa-dollar-sign fa-2x"></i>
                                    </div>
                                    <h4 class="mb-0">${{ number_format($stats['total_spent'] ?? 0, 2) }}</h4>
                                    <small class="text-muted">Total Spent</small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-3 mb-3">
                            <div class="card stat-card h-100">
                                <div class="card-body text-center">
                                    <div class="text-info mb-2">
                                        <i class="fas fa-heart fa-2x"></i>
                                    </div>
                                    <h4 class="mb-0">{{ $stats['wishlist_items'] ?? 0 }}</h4>
                                    <small class="text-muted">Wishlist Items</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Quick Actions -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0"><i class="fas fa-bolt me-2"></i>Quick Actions</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-3 mb-2">
                                            <a href="#" class="btn btn-primary w-100">
                                                <i class="fas fa-search me-2"></i>Browse Products
                                            </a>
                                        </div>
                                        <div class="col-md-3 mb-2">
                                            <a href="#" class="btn btn-success w-100">
                                                <i class="fas fa-shopping-bag me-2"></i>View Orders
                                            </a>
                                        </div>
                                        <div class="col-md-3 mb-2">
                                            <a href="#" class="btn btn-info w-100">
                                                <i class="fas fa-heart me-2"></i>My Wishlist
                                            </a>
                                        </div>
                                        <div class="col-md-3 mb-2">
                                            <a href="#" class="btn btn-warning w-100">
                                                <i class="fas fa-user-edit me-2"></i>Edit Profile
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Recent Orders & Recommended Products -->
                    <div class="row">
                        <div class="col-md-8">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0"><i class="fas fa-shopping-bag me-2"></i>Recent Orders</h5>
                                </div>
                                <div class="card-body">
                                    @if(isset($recent_orders) && count($recent_orders) > 0)
                                        @foreach($recent_orders as $order)
                                            <div class="d-flex align-items-center justify-content-between mb-3 p-3 border rounded">
                                                <div>
                                                    <h6 class="mb-1">Order #{{ $order['id'] }}</h6>
                                                    <small class="text-muted">{{ $order['date'] }} • {{ $order['items'] }} items</small>
                                                </div>
                                                <div class="text-end">
                                                    <div class="fw-bold">${{ $order['total'] }}</div>
                                                    <span class="badge bg-{{ $order['status_color'] }}">{{ $order['status'] }}</span>
                                                </div>
                                            </div>
                                        @endforeach
                                    @else
                                        <div class="text-center text-muted py-4">
                                            <i class="fas fa-shopping-bag fa-2x mb-3"></i>
                                            <p>No orders yet</p>
                                            <a href="#" class="btn btn-primary">Start Shopping</a>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0"><i class="fas fa-star me-2"></i>Recommended</h5>
                                </div>
                                <div class="card-body">
                                    @if(isset($recommended_products) && count($recommended_products) > 0)
                                        @foreach($recommended_products as $product)
                                            <div class="product-card mb-3">
                                                <div class="card-body p-3">
                                                    <h6 class="card-title mb-1">{{ $product['name'] }}</h6>
                                                    <p class="card-text text-muted small mb-2">{{ $product['description'] }}</p>
                                                    <div class="d-flex justify-content-between align-items-center">
                                                        <span class="fw-bold text-primary">${{ $product['price'] }}</span>
                                                        <div class="text-warning">
                                                            @for($i = 1; $i <= 5; $i++)
                                                                <i class="fas fa-star{{ $i <= $product['rating'] ? '' : '-o' }}"></i>
                                                            @endfor
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        @endforeach
                                    @else
                                        <div class="text-center text-muted py-4">
                                            <i class="fas fa-box fa-2x mb-3"></i>
                                            <p>No recommendations yet</p>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
