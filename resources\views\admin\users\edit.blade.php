@extends('admin.layout')

@section('title', 'Edit User')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-user-edit me-2"></i>Edit User</h2>
    <div>
        <a href="{{ route('admin.users.show', $user) }}" class="btn btn-outline-info">
            <i class="fas fa-eye me-2"></i>View User
        </a>
        <a href="{{ route('admin.users.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>Back to Users
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Edit User Information</h5>
            </div>
            <div class="card-body">
                @if ($errors->any())
                    <div class="alert alert-danger">
                        <ul class="mb-0">
                            @foreach ($errors->all() as $error)
                                <li>{{ $error }}</li>
                            @endforeach
                        </ul>
                    </div>
                @endif

                <form method="POST" action="{{ route('admin.users.update', $user) }}">
                    @csrf
                    @method('PUT')
                    
                    <!-- Personal Information -->
                    <h6 class="text-muted mb-3">Personal Information</h6>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="first_name" class="form-label">First Name *</label>
                            <input type="text" class="form-control @error('first_name') is-invalid @enderror" 
                                   id="first_name" name="first_name" 
                                   value="{{ old('first_name', $user->profile->first_name ?? '') }}" required>
                            @error('first_name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="last_name" class="form-label">Last Name *</label>
                            <input type="text" class="form-control @error('last_name') is-invalid @enderror" 
                                   id="last_name" name="last_name" 
                                   value="{{ old('last_name', $user->profile->last_name ?? '') }}" required>
                            @error('last_name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="name" class="form-label">Display Name *</label>
                        <input type="text" class="form-control @error('name') is-invalid @enderror" 
                               id="name" name="name" value="{{ old('name', $user->name) }}" required>
                        @error('name')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label">Email Address *</label>
                            <input type="email" class="form-control @error('email') is-invalid @enderror" 
                                   id="email" name="email" value="{{ old('email', $user->email) }}" required>
                            @error('email')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="phone" class="form-label">Phone Number</label>
                            <input type="tel" class="form-control @error('phone') is-invalid @enderror" 
                                   id="phone" name="phone" value="{{ old('phone', $user->phone) }}">
                            @error('phone')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="country" class="form-label">Country</label>
                            <input type="text" class="form-control @error('country') is-invalid @enderror" 
                                   id="country" name="country" value="{{ old('country', $user->country) }}">
                            @error('country')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="city" class="form-label">City</label>
                            <input type="text" class="form-control @error('city') is-invalid @enderror" 
                                   id="city" name="city" value="{{ old('city', $user->city) }}">
                            @error('city')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <hr class="my-4">

                    <!-- Account Settings -->
                    <h6 class="text-muted mb-3">Account Settings</h6>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="role" class="form-label">Role *</label>
                            <select class="form-select @error('role') is-invalid @enderror" id="role" name="role" required>
                                <option value="">Select Role</option>
                                <option value="buyer" {{ old('role', $user->role) == 'buyer' ? 'selected' : '' }}>Buyer</option>
                                <option value="seller" {{ old('role', $user->role) == 'seller' ? 'selected' : '' }}>Seller</option>
                                <option value="admin" {{ old('role', $user->role) == 'admin' ? 'selected' : '' }}>Admin</option>
                            </select>
                            @error('role')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="status" class="form-label">Status *</label>
                            <select class="form-select @error('status') is-invalid @enderror" id="status" name="status" required>
                                <option value="">Select Status</option>
                                <option value="active" {{ old('status', $user->status) == 'active' ? 'selected' : '' }}>Active</option>
                                <option value="inactive" {{ old('status', $user->status) == 'inactive' ? 'selected' : '' }}>Inactive</option>
                                <option value="suspended" {{ old('status', $user->status) == 'suspended' ? 'selected' : '' }}>Suspended</option>
                            </select>
                            @error('status')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Note:</strong> Leave password fields empty to keep the current password unchanged.
                    </div>

                    <div class="d-flex justify-content-end gap-2">
                        <a href="{{ route('admin.users.show', $user) }}" class="btn btn-secondary">Cancel</a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Update User
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <!-- Current User Info -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Current Information</h5>
            </div>
            <div class="card-body">
                <div class="text-center mb-3">
                    <div class="avatar-circle mx-auto mb-2" style="width: 60px; height: 60px; font-size: 24px;">
                        {{ strtoupper(substr($user->name, 0, 2)) }}
                    </div>
                    <h6>{{ $user->name }}</h6>
                    <div>
                        <span class="badge bg-{{ $user->role === 'admin' ? 'danger' : ($user->role === 'seller' ? 'primary' : 'success') }} me-1">
                            {{ ucfirst($user->role) }}
                        </span>
                        <span class="badge bg-{{ $user->status === 'active' ? 'success' : ($user->status === 'suspended' ? 'danger' : 'warning') }}">
                            {{ ucfirst($user->status) }}
                        </span>
                    </div>
                </div>
                
                <table class="table table-sm table-borderless">
                    <tr>
                        <td class="text-muted">Email:</td>
                        <td>{{ $user->email }}</td>
                    </tr>
                    <tr>
                        <td class="text-muted">Phone:</td>
                        <td>{{ $user->phone ?: 'Not set' }}</td>
                    </tr>
                    <tr>
                        <td class="text-muted">Location:</td>
                        <td>
                            @if($user->city || $user->country)
                                {{ $user->city }}{{ $user->city && $user->country ? ', ' : '' }}{{ $user->country }}
                            @else
                                Not set
                            @endif
                        </td>
                    </tr>
                    <tr>
                        <td class="text-muted">Joined:</td>
                        <td>{{ $user->created_at->format('M j, Y') }}</td>
                    </tr>
                </table>
            </div>
        </div>

        <!-- Role Information -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Role Information</h5>
            </div>
            <div class="card-body">
                <div class="role-info" id="buyer-info" style="display: none;">
                    <div class="text-success mb-2">
                        <i class="fas fa-shopping-cart fa-2x"></i>
                    </div>
                    <h6>Buyer Account</h6>
                    <p class="text-muted small">
                        Buyers can browse and purchase products/services from verified sellers.
                    </p>
                </div>
                
                <div class="role-info" id="seller-info" style="display: none;">
                    <div class="text-primary mb-2">
                        <i class="fas fa-store fa-2x"></i>
                    </div>
                    <h6>Seller Account</h6>
                    <p class="text-muted small">
                        Sellers can list and sell products/services after verification.
                    </p>
                </div>
                
                <div class="role-info" id="admin-info" style="display: none;">
                    <div class="text-danger mb-2">
                        <i class="fas fa-crown fa-2x"></i>
                    </div>
                    <h6>Admin Account</h6>
                    <p class="text-muted small">
                        Administrators have full system access and management capabilities.
                    </p>
                </div>
                
                <div class="role-info" id="default-info">
                    <div class="text-muted mb-2">
                        <i class="fas fa-user fa-2x"></i>
                    </div>
                    <h6>Select a Role</h6>
                    <p class="text-muted small">
                        Choose a role to see the permissions and capabilities.
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
    .avatar-circle {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        font-size: 14px;
    }
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const roleSelect = document.getElementById('role');
    
    // Show current role info on load
    showRoleInfo(roleSelect.value);
    
    roleSelect.addEventListener('change', function() {
        showRoleInfo(this.value);
    });

    function showRoleInfo(role) {
        const infoDivs = document.querySelectorAll('.role-info');
        
        infoDivs.forEach(div => div.style.display = 'none');
        
        if (role) {
            const targetDiv = document.getElementById(role + '-info');
            if (targetDiv) {
                targetDiv.style.display = 'block';
            }
        } else {
            document.getElementById('default-info').style.display = 'block';
        }
    }
});
</script>
@endpush
