<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('products', function (Blueprint $table) {
            $table->id();
            $table->foreignId('seller_id')->constrained()->onDelete('cascade');
            $table->foreignId('category_id')->constrained()->onDelete('restrict');
            $table->string('title');
            $table->string('title_ar')->nullable(); // Arabic title
            $table->string('slug')->unique();
            $table->text('description');
            $table->text('description_ar')->nullable(); // Arabic description
            $table->enum('type', ['product', 'service'])->default('service');
            $table->decimal('price', 10, 2);
            $table->decimal('discount_price', 10, 2)->nullable();
            $table->string('currency', 3)->default('USD');
            $table->integer('delivery_time')->default(1); // Days
            $table->integer('revisions')->default(1); // Number of revisions included
            $table->json('images')->nullable(); // Array of image paths
            $table->json('gallery')->nullable(); // Additional gallery images
            $table->json('features')->nullable(); // What's included
            $table->json('requirements')->nullable(); // What seller needs from buyer
            $table->json('packages')->nullable(); // Basic, Standard, Premium packages
            $table->json('tags')->nullable(); // Search tags
            $table->json('faq')->nullable(); // Frequently asked questions
            $table->enum('status', ['draft', 'active', 'paused', 'rejected'])->default('draft');
            $table->text('rejection_reason')->nullable();
            $table->integer('views')->default(0);
            $table->integer('orders_count')->default(0);
            $table->decimal('average_rating', 3, 2)->default(0);
            $table->integer('reviews_count')->default(0);
            $table->boolean('is_featured')->default(false);
            $table->timestamp('featured_until')->nullable();
            $table->json('meta_data')->nullable(); // SEO and additional data
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('products');
    }
};
