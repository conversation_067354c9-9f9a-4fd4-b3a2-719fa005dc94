@extends('admin.layout')

@section('title', 'Dispute Details - ' . $dispute->dispute_number)

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2><i class="fas fa-exclamation-triangle me-2"></i>Dispute Details</h2>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
                <li class="breadcrumb-item"><a href="{{ route('admin.disputes.index') }}">Disputes</a></li>
                <li class="breadcrumb-item active">{{ $dispute->dispute_number }}</li>
            </ol>
        </nav>
    </div>
    <div class="btn-group">
        <button type="button" class="btn btn-primary dropdown-toggle" data-bs-toggle="dropdown">
            <i class="fas fa-cog me-1"></i>Actions
        </button>
        <ul class="dropdown-menu">
            @if($dispute->status == 'open')
                <li><a class="dropdown-item" href="#" onclick="updateDisputeStatus('investigating')">
                    <i class="fas fa-search me-2 text-warning"></i>Start Investigation
                </a></li>
            @endif
            @if($dispute->status != 'resolved')
                <li><a class="dropdown-item" href="#" onclick="resolveDispute()">
                    <i class="fas fa-check me-2 text-success"></i>Mark Resolved
                </a></li>
            @endif
            <li><a class="dropdown-item" href="#" onclick="escalatePriority()">
                <i class="fas fa-arrow-up me-2 text-danger"></i>Escalate Priority
            </a></li>
            <li><hr class="dropdown-divider"></li>
            <li><a class="dropdown-item" href="#" onclick="closeDispute()">
                <i class="fas fa-times me-2 text-secondary"></i>Close Dispute
            </a></li>
        </ul>
    </div>
</div>

<!-- Dispute Status Alert -->
@if($dispute->status == 'open')
    <div class="alert alert-danger" role="alert">
        <i class="fas fa-exclamation-triangle me-2"></i>
        <strong>Open Dispute:</strong> This dispute requires immediate attention and investigation.
    </div>
@elseif($dispute->status == 'investigating')
    <div class="alert alert-warning" role="alert">
        <i class="fas fa-search me-2"></i>
        <strong>Under Investigation:</strong> This dispute is currently being investigated by the admin team.
    </div>
@elseif($dispute->status == 'resolved')
    <div class="alert alert-success" role="alert">
        <i class="fas fa-check-circle me-2"></i>
        <strong>Resolved:</strong> This dispute has been successfully resolved.
    </div>
@endif

<!-- Dispute Overview -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Dispute Information</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-muted">Dispute Details</h6>
                        <p><strong>Dispute Number:</strong> {{ $dispute->dispute_number }}</p>
                        <p><strong>Related Order:</strong> 
                            <a href="#" class="text-decoration-none">{{ $dispute->order_number }}</a>
                        </p>
                        <p><strong>Subject:</strong> {{ $dispute->subject }}</p>
                        <p><strong>Amount in Dispute:</strong> 
                            <span class="text-danger">${{ number_format($dispute->amount, 2) }}</span>
                        </p>
                        <p><strong>Created:</strong> {{ $dispute->created_at->format('M j, Y g:i A') }}</p>
                        <p><strong>Last Activity:</strong> {{ $dispute->last_activity->diffForHumans() }}</p>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-muted">Description</h6>
                        <p>{{ $dispute->description }}</p>
                        
                        @if($dispute->evidence)
                            <h6 class="text-muted mt-3">Evidence Provided</h6>
                            <ul class="list-unstyled">
                                @foreach($dispute->evidence as $evidence)
                                    <li><i class="fas fa-paperclip me-2"></i>{{ $evidence }}</li>
                                @endforeach
                            </ul>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Dispute Status</h5>
            </div>
            <div class="card-body text-center">
                @php
                    $statusClasses = [
                        'open' => 'danger',
                        'investigating' => 'warning',
                        'resolved' => 'success',
                        'closed' => 'secondary'
                    ];
                    $statusIcons = [
                        'open' => 'exclamation-triangle',
                        'investigating' => 'search',
                        'resolved' => 'check-circle',
                        'closed' => 'times-circle'
                    ];
                    $priorityClasses = [
                        'high' => 'danger',
                        'medium' => 'warning',
                        'low' => 'info'
                    ];
                @endphp
                <div class="text-{{ $statusClasses[$dispute->status] ?? 'secondary' }} mb-3">
                    <i class="fas fa-{{ $statusIcons[$dispute->status] ?? 'question' }} fa-3x"></i>
                </div>
                <h4 class="text-{{ $statusClasses[$dispute->status] ?? 'secondary' }}">
                    {{ ucfirst($dispute->status) }}
                </h4>
                <div class="mt-3">
                    <span class="badge bg-{{ $priorityClasses[$dispute->priority] ?? 'secondary' }} fs-6">
                        {{ ucfirst($dispute->priority) }} Priority
                    </span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Parties Information -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Buyer Information</h5>
            </div>
            <div class="card-body">
                <div class="d-flex align-items-center mb-3">
                    <div class="avatar-circle me-3">
                        {{ strtoupper(substr($dispute->buyer_name, 0, 2)) }}
                    </div>
                    <div>
                        <h6 class="mb-0">{{ $dispute->buyer_name }}</h6>
                        <small class="text-muted">{{ $dispute->buyer_email }}</small>
                    </div>
                </div>
                <div class="btn-group btn-group-sm">
                    <a href="mailto:{{ $dispute->buyer_email }}" class="btn btn-outline-primary">
                        <i class="fas fa-envelope me-1"></i>Email
                    </a>
                    <button type="button" class="btn btn-outline-info">
                        <i class="fas fa-user me-1"></i>View Profile
                    </button>
                    <button type="button" class="btn btn-outline-warning">
                        <i class="fas fa-history me-1"></i>Order History
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Seller Information</h5>
            </div>
            <div class="card-body">
                <div class="d-flex align-items-center mb-3">
                    <div class="avatar-circle me-3">
                        {{ strtoupper(substr($dispute->seller_name, 0, 2)) }}
                    </div>
                    <div>
                        <h6 class="mb-0">{{ $dispute->seller_name }}</h6>
                        <small class="text-muted">{{ $dispute->seller_email }}</small>
                    </div>
                </div>
                <div class="btn-group btn-group-sm">
                    <a href="mailto:{{ $dispute->seller_email }}" class="btn btn-outline-primary">
                        <i class="fas fa-envelope me-1"></i>Email
                    </a>
                    <button type="button" class="btn btn-outline-info">
                        <i class="fas fa-store me-1"></i>View Store
                    </button>
                    <button type="button" class="btn btn-outline-warning">
                        <i class="fas fa-chart-line me-1"></i>Performance
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Communication Thread -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Communication Thread</h5>
                <button type="button" class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#addMessageModal">
                    <i class="fas fa-plus me-1"></i>Add Message
                </button>
            </div>
            <div class="card-body">
                <div class="communication-thread">
                    @foreach($dispute->messages as $message)
                        <div class="message-item mb-3">
                            <div class="d-flex justify-content-between align-items-start">
                                <div class="d-flex align-items-center">
                                    <div class="avatar-circle me-3" style="width: 35px; height: 35px; font-size: 12px;">
                                        {{ strtoupper(substr(explode(' ', $message->sender)[0], 0, 2)) }}
                                    </div>
                                    <div>
                                        <h6 class="mb-0">{{ $message->sender }}</h6>
                                        <small class="text-muted">{{ $message->timestamp->format('M j, Y g:i A') }}</small>
                                    </div>
                                </div>
                            </div>
                            <div class="message-content mt-2 ms-5">
                                <div class="bg-light p-3 rounded">
                                    {{ $message->message }}
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Quick Actions</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button type="button" class="btn btn-outline-primary" onclick="sendTemplate('request_info')">
                        <i class="fas fa-info-circle me-2"></i>Request More Info
                    </button>
                    <button type="button" class="btn btn-outline-warning" onclick="sendTemplate('mediation')">
                        <i class="fas fa-handshake me-2"></i>Suggest Mediation
                    </button>
                    <button type="button" class="btn btn-outline-success" onclick="sendTemplate('refund')">
                        <i class="fas fa-money-bill-wave me-2"></i>Process Refund
                    </button>
                    <button type="button" class="btn btn-outline-info" onclick="sendTemplate('escalate')">
                        <i class="fas fa-arrow-up me-2"></i>Escalate to Manager
                    </button>
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">Resolution Tools</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button type="button" class="btn btn-success" onclick="resolveInFavorOf('buyer')">
                        <i class="fas fa-user me-2"></i>Resolve for Buyer
                    </button>
                    <button type="button" class="btn btn-info" onclick="resolveInFavorOf('seller')">
                        <i class="fas fa-store me-2"></i>Resolve for Seller
                    </button>
                    <button type="button" class="btn btn-warning" onclick="suggestCompromise()">
                        <i class="fas fa-balance-scale me-2"></i>Suggest Compromise
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Message Modal -->
<div class="modal fade" id="addMessageModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add Message to Dispute</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="messageForm">
                    <div class="mb-3">
                        <label for="messageContent" class="form-label">Message</label>
                        <textarea class="form-control" id="messageContent" rows="5" 
                                  placeholder="Type your message here..." required></textarea>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="notifyParties">
                            <label class="form-check-label" for="notifyParties">
                                Notify both parties via email
                            </label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="addMessage()">Send Message</button>
            </div>
        </div>
    </div>
</div>

<!-- Resolution Modal -->
<div class="modal fade" id="resolutionModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Resolve Dispute</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="resolutionForm">
                    <div class="mb-3">
                        <label for="resolutionSummary" class="form-label">Resolution Summary</label>
                        <textarea class="form-control" id="resolutionSummary" rows="4" 
                                  placeholder="Provide a summary of how this dispute was resolved..." required></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="resolutionAction" class="form-label">Action Taken</label>
                        <select class="form-select" id="resolutionAction" required>
                            <option value="">Select action...</option>
                            <option value="refund_full">Full Refund to Buyer</option>
                            <option value="refund_partial">Partial Refund to Buyer</option>
                            <option value="replacement">Product Replacement</option>
                            <option value="store_credit">Store Credit Issued</option>
                            <option value="no_action">No Action Required</option>
                            <option value="other">Other (specify in summary)</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-success" onclick="confirmResolution()">Resolve Dispute</button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.avatar-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 14px;
}

.communication-thread {
    max-height: 500px;
    overflow-y: auto;
}

.message-item {
    border-left: 3px solid #007bff;
    padding-left: 15px;
}

.message-content {
    margin-left: 20px;
}
</style>
@endpush

@push('scripts')
<script>
function updateDisputeStatus(status) {
    if (confirm('Are you sure you want to update this dispute status to: ' + status + '?')) {
        // In a real implementation, make AJAX call to update status
        console.log('Updating dispute status to:', status);
        alert('Dispute status would be updated to: ' + status);
    }
}

function resolveDispute() {
    const modal = new bootstrap.Modal(document.getElementById('resolutionModal'));
    modal.show();
}

function confirmResolution() {
    const summary = document.getElementById('resolutionSummary').value;
    const action = document.getElementById('resolutionAction').value;
    
    if (summary.trim() && action) {
        // In a real implementation, make AJAX call to resolve dispute
        console.log('Resolving dispute with summary:', summary, 'and action:', action);
        alert('Dispute would be resolved');
        bootstrap.Modal.getInstance(document.getElementById('resolutionModal')).hide();
    } else {
        alert('Please provide both a summary and select an action');
    }
}

function escalatePriority() {
    if (confirm('Are you sure you want to escalate this dispute to high priority?')) {
        // In a real implementation, make AJAX call to escalate
        console.log('Escalating dispute priority');
        alert('Dispute priority would be escalated');
    }
}

function closeDispute() {
    if (confirm('Are you sure you want to close this dispute?')) {
        // In a real implementation, make AJAX call to close
        console.log('Closing dispute');
        alert('Dispute would be closed');
    }
}

function addMessage() {
    const message = document.getElementById('messageContent').value;
    const notify = document.getElementById('notifyParties').checked;
    
    if (message.trim()) {
        // In a real implementation, make AJAX call to add message
        console.log('Adding message:', message, 'Notify parties:', notify);
        alert('Message would be added to the dispute');
        bootstrap.Modal.getInstance(document.getElementById('addMessageModal')).hide();
        document.getElementById('messageForm').reset();
    } else {
        alert('Please enter a message');
    }
}

function sendTemplate(template) {
    const templates = {
        'request_info': 'We need additional information to proceed with this dispute. Please provide more details.',
        'mediation': 'We suggest both parties work together to find a mutually acceptable solution.',
        'refund': 'Based on our investigation, we recommend processing a refund for this order.',
        'escalate': 'This dispute has been escalated to our senior management team for review.'
    };
    
    document.getElementById('messageContent').value = templates[template];
    const modal = new bootstrap.Modal(document.getElementById('addMessageModal'));
    modal.show();
}

function resolveInFavorOf(party) {
    if (confirm('Are you sure you want to resolve this dispute in favor of the ' + party + '?')) {
        // In a real implementation, make AJAX call to resolve
        console.log('Resolving in favor of:', party);
        alert('Dispute would be resolved in favor of the ' + party);
    }
}

function suggestCompromise() {
    const compromise = prompt('Please describe the compromise solution:');
    if (compromise) {
        // In a real implementation, make AJAX call to suggest compromise
        console.log('Suggesting compromise:', compromise);
        alert('Compromise suggestion would be sent to both parties');
    }
}
</script>
@endpush
