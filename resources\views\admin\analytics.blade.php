@extends('admin.layout')

@section('title', 'Analytics Dashboard')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-chart-bar me-2"></i>Analytics Dashboard</h2>
    <div class="btn-group">
        <button type="button" class="btn btn-outline-primary active" data-period="7">7 Days</button>
        <button type="button" class="btn btn-outline-primary" data-period="30">30 Days</button>
        <button type="button" class="btn btn-outline-primary" data-period="90">90 Days</button>
    </div>
</div>

<!-- Key Metrics -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card stat-card text-center">
            <div class="card-body">
                <div class="text-primary mb-2">
                    <i class="fas fa-users fa-2x"></i>
                </div>
                <h3 class="mb-0">{{ \App\Models\User::count() }}</h3>
                <small class="text-muted">Total Users</small>
                <div class="mt-2">
                    <small class="text-success">
                        <i class="fas fa-arrow-up"></i> +12% this week
                    </small>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-3 mb-3">
        <div class="card stat-card text-center">
            <div class="card-body">
                <div class="text-success mb-2">
                    <i class="fas fa-store fa-2x"></i>
                </div>
                <h3 class="mb-0">{{ \App\Models\Seller::where('verification_status', 'approved')->count() }}</h3>
                <small class="text-muted">Active Sellers</small>
                <div class="mt-2">
                    <small class="text-success">
                        <i class="fas fa-arrow-up"></i> +8% this week
                    </small>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-3 mb-3">
        <div class="card stat-card text-center">
            <div class="card-body">
                <div class="text-warning mb-2">
                    <i class="fas fa-shopping-cart fa-2x"></i>
                </div>
                <h3 class="mb-0">0</h3>
                <small class="text-muted">Total Orders</small>
                <div class="mt-2">
                    <small class="text-muted">
                        Coming soon
                    </small>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-3 mb-3">
        <div class="card stat-card text-center">
            <div class="card-body">
                <div class="text-info mb-2">
                    <i class="fas fa-dollar-sign fa-2x"></i>
                </div>
                <h3 class="mb-0">$0</h3>
                <small class="text-muted">Total Revenue</small>
                <div class="mt-2">
                    <small class="text-muted">
                        Coming soon
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Charts Row -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">User Registration Trends</h5>
            </div>
            <div class="card-body">
                <canvas id="userRegistrationChart" height="100"></canvas>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">User Distribution</h5>
            </div>
            <div class="card-body">
                <canvas id="userDistributionChart"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Detailed Statistics -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">User Statistics</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-6 text-center mb-3">
                        <h4 class="text-success">{{ \App\Models\User::where('role', 'buyer')->count() }}</h4>
                        <small class="text-muted">Buyers</small>
                    </div>
                    <div class="col-6 text-center mb-3">
                        <h4 class="text-primary">{{ \App\Models\User::where('role', 'seller')->count() }}</h4>
                        <small class="text-muted">Sellers</small>
                    </div>
                    <div class="col-6 text-center mb-3">
                        <h4 class="text-warning">{{ \App\Models\User::where('status', 'active')->count() }}</h4>
                        <small class="text-muted">Active Users</small>
                    </div>
                    <div class="col-6 text-center mb-3">
                        <h4 class="text-danger">{{ \App\Models\User::where('role', 'admin')->count() }}</h4>
                        <small class="text-muted">Administrators</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Seller Verification Status</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-4 text-center mb-3">
                        <h4 class="text-success">{{ \App\Models\Seller::where('verification_status', 'approved')->count() }}</h4>
                        <small class="text-muted">Approved</small>
                    </div>
                    <div class="col-4 text-center mb-3">
                        <h4 class="text-warning">{{ \App\Models\Seller::where('verification_status', 'pending')->count() }}</h4>
                        <small class="text-muted">Pending</small>
                    </div>
                    <div class="col-4 text-center mb-3">
                        <h4 class="text-danger">{{ \App\Models\Seller::where('verification_status', 'rejected')->count() }}</h4>
                        <small class="text-muted">Rejected</small>
                    </div>
                </div>

                <div class="progress mb-3" style="height: 10px;">
                    @php
                        $total_sellers = \App\Models\Seller::count();
                        $approved = \App\Models\Seller::where('verification_status', 'approved')->count();
                        $pending = \App\Models\Seller::where('verification_status', 'pending')->count();
                        $rejected = \App\Models\Seller::where('verification_status', 'rejected')->count();

                        $approved_percent = $total_sellers > 0 ? ($approved / $total_sellers) * 100 : 0;
                        $pending_percent = $total_sellers > 0 ? ($pending / $total_sellers) * 100 : 0;
                        $rejected_percent = $total_sellers > 0 ? ($rejected / $total_sellers) * 100 : 0;
                    @endphp

                    <div class="progress-bar bg-success" style="width: {{ round($approved_percent, 2) }}%"></div>
                    <div class="progress-bar bg-warning" style="width: {{ round($pending_percent, 2) }}%"></div>
                    <div class="progress-bar bg-danger" style="width: {{ round($rejected_percent, 2) }}%"></div>
                </div>

                <div class="d-flex justify-content-between small text-muted">
                    <span>{{ number_format($approved_percent, 1) }}% Approved</span>
                    <span>{{ number_format($pending_percent, 1) }}% Pending</span>
                    <span>{{ number_format($rejected_percent, 1) }}% Rejected</span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activity -->
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Recent Platform Activity</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>Activity</th>
                                <th>User</th>
                                <th>Type</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            @php
                                $recent_users = \App\Models\User::latest()->take(10)->get();
                            @endphp
                            @foreach($recent_users as $recent_user)
                                <tr>
                                    <td>{{ $recent_user->created_at->format('M j, Y g:i A') }}</td>
                                    <td>User Registration</td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-circle me-2" style="width: 30px; height: 30px; font-size: 12px;">
                                                {{ strtoupper(substr($recent_user->name, 0, 2)) }}
                                            </div>
                                            {{ $recent_user->name }}
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-{{ $recent_user->role === 'admin' ? 'danger' : ($recent_user->role === 'seller' ? 'primary' : 'success') }}">
                                            {{ ucfirst($recent_user->role) }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-{{ $recent_user->status === 'active' ? 'success' : 'warning' }}">
                                            {{ ucfirst($recent_user->status) }}
                                        </span>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // User Registration Chart
    const userCtx = document.getElementById('userRegistrationChart').getContext('2d');
    new Chart(userCtx, {
        type: 'line',
        data: {
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul'],
            datasets: [{
                label: 'New Users',
                data: [12, 19, 8, 15, 25, 22, 30],
                borderColor: 'rgb(75, 192, 192)',
                backgroundColor: 'rgba(75, 192, 192, 0.1)',
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    // User Distribution Chart
    const distributionCtx = document.getElementById('userDistributionChart').getContext('2d');
    new Chart(distributionCtx, {
        type: 'doughnut',
        data: {
            labels: ['Buyers', 'Sellers', 'Admins'],
            datasets: [{
                data: [
                    {{ $userStats['buyers'] ?? 0 }},
                    {{ $userStats['sellers'] ?? 0 }},
                    {{ $userStats['admins'] ?? 0 }}
                ],
                backgroundColor: [
                    '#28a745',
                    '#007bff',
                    '#dc3545'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: true,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });

    // Period filter buttons
    document.querySelectorAll('[data-period]').forEach(button => {
        button.addEventListener('click', function() {
            document.querySelectorAll('[data-period]').forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');

            // Here you would typically reload the charts with new data
            console.log('Loading data for period:', this.dataset.period);
        });
    });
});
</script>
@endpush

@push('styles')
<style>
    .avatar-circle {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        font-size: 14px;
    }
</style>
@endpush
