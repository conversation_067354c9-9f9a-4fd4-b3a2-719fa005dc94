@extends('admin.layout')

@section('title', 'User Details')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-user me-2"></i>User Details</h2>
    <div>
        <a href="{{ route('admin.users.edit', $user) }}" class="btn btn-warning">
            <i class="fas fa-edit me-2"></i>Edit User
        </a>
        <a href="{{ route('admin.users.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>Back to Users
        </a>
    </div>
</div>

<div class="row">
    <!-- User Information -->
    <div class="col-md-8">
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">User Information</h5>
                <div>
                    <span class="badge bg-{{ $user->role === 'admin' ? 'danger' : ($user->role === 'seller' ? 'primary' : 'success') }} me-2">
                        {{ ucfirst($user->role) }}
                    </span>
                    <span class="badge bg-{{ $user->status === 'active' ? 'success' : ($user->status === 'suspended' ? 'danger' : 'warning') }}">
                        {{ ucfirst($user->status) }}
                    </span>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td class="fw-bold">Display Name:</td>
                                <td>{{ $user->name }}</td>
                            </tr>
                            <tr>
                                <td class="fw-bold">Email:</td>
                                <td>
                                    {{ $user->email }}
                                    @if($user->email_verified_at)
                                        <i class="fas fa-check-circle text-success ms-1" title="Verified"></i>
                                    @else
                                        <i class="fas fa-exclamation-circle text-warning ms-1" title="Not Verified"></i>
                                    @endif
                                </td>
                            </tr>
                            <tr>
                                <td class="fw-bold">Phone:</td>
                                <td>{{ $user->phone ?: 'Not provided' }}</td>
                            </tr>
                            <tr>
                                <td class="fw-bold">Language:</td>
                                <td>{{ strtoupper($user->language) }}</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td class="fw-bold">First Name:</td>
                                <td>{{ $user->profile->first_name ?? 'Not provided' }}</td>
                            </tr>
                            <tr>
                                <td class="fw-bold">Last Name:</td>
                                <td>{{ $user->profile->last_name ?? 'Not provided' }}</td>
                            </tr>
                            <tr>
                                <td class="fw-bold">Location:</td>
                                <td>
                                    @if($user->city || $user->country)
                                        {{ $user->city }}{{ $user->city && $user->country ? ', ' : '' }}{{ $user->country }}
                                    @else
                                        Not provided
                                    @endif
                                </td>
                            </tr>
                            <tr>
                                <td class="fw-bold">Timezone:</td>
                                <td>{{ $user->profile->timezone ?? 'UTC' }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
                
                <div class="row mt-3">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td class="fw-bold">Joined:</td>
                                <td>{{ $user->created_at->format('F j, Y \a\t g:i A') }}</td>
                            </tr>
                            <tr>
                                <td class="fw-bold">Last Updated:</td>
                                <td>{{ $user->updated_at->format('F j, Y \a\t g:i A') }}</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        @if($user->profile && $user->profile->identity_verified)
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle me-2"></i>
                                Identity verified on {{ $user->profile->identity_verified_at->format('M j, Y') }}
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>

        <!-- Seller Information (if applicable) -->
        @if($user->role === 'seller' && $user->seller)
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Seller Information</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td class="fw-bold">Business Name:</td>
                                    <td>{{ $user->seller->business_name }}</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">Business Type:</td>
                                    <td>{{ ucfirst($user->seller->business_type) }}</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">Verification Status:</td>
                                    <td>
                                        <span class="badge bg-{{ $user->seller->verification_status === 'approved' ? 'success' : ($user->seller->verification_status === 'rejected' ? 'danger' : 'warning') }}">
                                            {{ ucfirst($user->seller->verification_status) }}
                                        </span>
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td class="fw-bold">Active Status:</td>
                                    <td>
                                        <span class="badge bg-{{ $user->seller->is_active ? 'success' : 'secondary' }}">
                                            {{ $user->seller->is_active ? 'Active' : 'Inactive' }}
                                        </span>
                                    </td>
                                </tr>
                                @if($user->seller->verified_at)
                                    <tr>
                                        <td class="fw-bold">Verified Date:</td>
                                        <td>{{ $user->seller->verified_at->format('M j, Y') }}</td>
                                    </tr>
                                @endif
                                @if($user->seller->business_address)
                                    <tr>
                                        <td class="fw-bold">Business Address:</td>
                                        <td>{{ $user->seller->business_address }}</td>
                                    </tr>
                                @endif
                            </table>
                        </div>
                    </div>
                    
                    @if($user->profile && ($user->profile->skills || $user->profile->experience))
                        <hr>
                        @if($user->profile->skills)
                            <div class="mb-3">
                                <strong>Skills:</strong>
                                <p class="mb-0">{{ $user->profile->skills }}</p>
                            </div>
                        @endif
                        @if($user->profile->experience)
                            <div class="mb-3">
                                <strong>Experience:</strong>
                                <p class="mb-0">{{ $user->profile->experience }}</p>
                            </div>
                        @endif
                    @endif
                </div>
            </div>
        @endif

        <!-- Recent Activity -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Recent Activity</h5>
            </div>
            <div class="card-body">
                @if($user->orders && $user->orders->count() > 0)
                    <h6>Recent Orders ({{ $user->orders->count() }} total)</h6>
                    <!-- Orders would be displayed here -->
                    <p class="text-muted">Order history will be displayed here when order system is implemented.</p>
                @endif
                
                @if($user->reviews && $user->reviews->count() > 0)
                    <h6>Reviews ({{ $user->reviews->count() }} total)</h6>
                    <!-- Reviews would be displayed here -->
                    <p class="text-muted">Review history will be displayed here when review system is implemented.</p>
                @endif
                
                @if((!$user->orders || $user->orders->count() === 0) && (!$user->reviews || $user->reviews->count() === 0))
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-clock fa-2x mb-3"></i>
                        <p>No recent activity</p>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Actions Sidebar -->
    <div class="col-md-4">
        <!-- Quick Actions -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Quick Actions</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button type="button" class="btn btn-outline-primary" onclick="changeStatus('{{ $user->id }}')">
                        <i class="fas fa-toggle-on me-2"></i>Change Status
                    </button>
                    
                    <button type="button" class="btn btn-outline-warning" onclick="resetPassword('{{ $user->id }}')">
                        <i class="fas fa-key me-2"></i>Reset Password
                    </button>
                    
                    @if($user->role === 'seller' && $user->seller)
                        <a href="{{ route('admin.sellers.show', $user->seller) }}" class="btn btn-outline-info">
                            <i class="fas fa-store me-2"></i>View Seller Profile
                        </a>
                    @endif
                    
                    @if($user->id !== auth()->id())
                        <button type="button" class="btn btn-outline-danger" onclick="deleteUser('{{ $user->id }}')">
                            <i class="fas fa-trash me-2"></i>Delete User
                        </button>
                    @endif
                </div>
            </div>
        </div>

        <!-- User Statistics -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Statistics</h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6 mb-3">
                        <div class="border-end">
                            <h4 class="text-primary mb-0">{{ $user->orders ? $user->orders->count() : 0 }}</h4>
                            <small class="text-muted">Orders</small>
                        </div>
                    </div>
                    <div class="col-6 mb-3">
                        <h4 class="text-success mb-0">{{ $user->reviews ? $user->reviews->count() : 0 }}</h4>
                        <small class="text-muted">Reviews</small>
                    </div>
                </div>
                
                <hr>
                
                <div class="text-center">
                    <small class="text-muted">
                        Member for {{ $user->created_at->diffForHumans(null, true) }}
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Status Change Modal -->
<div class="modal fade" id="statusModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Change User Status</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="statusForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Select New Status</label>
                        <select class="form-select" name="status" required>
                            <option value="active" {{ $user->status === 'active' ? 'selected' : '' }}>Active</option>
                            <option value="inactive" {{ $user->status === 'inactive' ? 'selected' : '' }}>Inactive</option>
                            <option value="suspended" {{ $user->status === 'suspended' ? 'selected' : '' }}>Suspended</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update Status</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Password Reset Modal -->
<div class="modal fade" id="passwordModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Reset Password</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ route('admin.users.reset-password', $user) }}">
                @csrf
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">New Password</label>
                        <input type="password" class="form-control" name="password" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Confirm Password</label>
                        <input type="password" class="form-control" name="password_confirmation" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-warning">Reset Password</button>
                </div>
            </form>
        </div>
    </div>
</div>

@endsection

@push('scripts')
<script>
function changeStatus(userId) {
    new bootstrap.Modal(document.getElementById('statusModal')).show();
    
    document.getElementById('statusForm').onsubmit = function(e) {
        e.preventDefault();
        const formData = new FormData(this);
        
        fetch(`/admin/users/${userId}/status`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': '{{ csrf_token() }}',
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                status: formData.get('status')
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            }
        });
    };
}

function resetPassword(userId) {
    new bootstrap.Modal(document.getElementById('passwordModal')).show();
}

function deleteUser(userId) {
    if (confirm('Are you sure you want to delete this user? This action cannot be undone.')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/admin/users/${userId}`;
        form.innerHTML = `
            @csrf
            @method('DELETE')
        `;
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
@endpush
