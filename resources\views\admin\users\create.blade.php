@extends('admin.layout')

@section('title', 'Add New User')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-user-plus me-2"></i>Add New User</h2>
    <a href="{{ route('admin.users.index') }}" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-2"></i>Back to Users
    </a>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">User Information</h5>
            </div>
            <div class="card-body">
                @if ($errors->any())
                    <div class="alert alert-danger">
                        <ul class="mb-0">
                            @foreach ($errors->all() as $error)
                                <li>{{ $error }}</li>
                            @endforeach
                        </ul>
                    </div>
                @endif

                <form method="POST" action="{{ route('admin.users.store') }}">
                    @csrf

                    <!-- Personal Information -->
                    <h6 class="text-muted mb-3">Personal Information</h6>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="first_name" class="form-label">First Name *</label>
                            <input type="text" class="form-control @error('first_name') is-invalid @enderror"
                                   id="first_name" name="first_name" value="{{ old('first_name') }}" required>
                            @error('first_name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="last_name" class="form-label">Last Name *</label>
                            <input type="text" class="form-control @error('last_name') is-invalid @enderror"
                                   id="last_name" name="last_name" value="{{ old('last_name') }}" required>
                            @error('last_name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="name" class="form-label">Display Name *</label>
                        <input type="text" class="form-control @error('name') is-invalid @enderror"
                               id="name" name="name" value="{{ old('name') }}" required>
                        @error('name')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label">Email Address *</label>
                            <input type="email" class="form-control @error('email') is-invalid @enderror"
                                   id="email" name="email" value="{{ old('email') }}" required>
                            @error('email')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="phone" class="form-label">Phone Number</label>
                            <input type="tel" class="form-control @error('phone') is-invalid @enderror"
                                   id="phone" name="phone" value="{{ old('phone') }}">
                            @error('phone')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="country" class="form-label">Country</label>
                            <input type="text" class="form-control @error('country') is-invalid @enderror"
                                   id="country" name="country" value="{{ old('country') }}">
                            @error('country')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="city" class="form-label">City</label>
                            <input type="text" class="form-control @error('city') is-invalid @enderror"
                                   id="city" name="city" value="{{ old('city') }}">
                            @error('city')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <hr class="my-4">

                    <!-- Account Settings -->
                    <h6 class="text-muted mb-3">Account Settings</h6>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="role" class="form-label">Role *</label>
                            <select class="form-select @error('role') is-invalid @enderror" id="role" name="role" required>
                                <option value="">Select Role</option>
                                <option value="buyer" {{ old('role') == 'buyer' ? 'selected' : '' }}>Buyer</option>
                                <option value="seller" {{ old('role') == 'seller' ? 'selected' : '' }}>Seller</option>
                                <option value="admin" {{ old('role') == 'admin' ? 'selected' : '' }}>Admin</option>
                            </select>
                            @error('role')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="status" class="form-label">Status *</label>
                            <select class="form-select @error('status') is-invalid @enderror" id="status" name="status" required>
                                <option value="">Select Status</option>
                                <option value="active" {{ old('status') == 'active' ? 'selected' : '' }}>Active</option>
                                <option value="inactive" {{ old('status') == 'inactive' ? 'selected' : '' }}>Inactive</option>
                                <option value="suspended" {{ old('status') == 'suspended' ? 'selected' : '' }}>Suspended</option>
                            </select>
                            @error('status')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="password" class="form-label">Password *</label>
                            <input type="password" class="form-control @error('password') is-invalid @enderror"
                                   id="password" name="password" required>
                            @error('password')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="password_confirmation" class="form-label">Confirm Password *</label>
                            <input type="password" class="form-control"
                                   id="password_confirmation" name="password_confirmation" required>
                        </div>
                    </div>

                    <div class="d-flex justify-content-end gap-2">
                        <a href="{{ route('admin.users.index') }}" class="btn btn-secondary">Cancel</a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Create User
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Role Information</h5>
            </div>
            <div class="card-body">
                <div class="role-info" id="buyer-info" style="display: none;">
                    <div class="text-success mb-2">
                        <i class="fas fa-shopping-cart fa-2x"></i>
                    </div>
                    <h6>Buyer Account</h6>
                    <p class="text-muted small">
                        Buyers can browse and purchase products/services from verified sellers.
                        They have access to order tracking, reviews, and customer support.
                    </p>
                    <ul class="list-unstyled small">
                        <li><i class="fas fa-check text-success me-2"></i>Browse marketplace</li>
                        <li><i class="fas fa-check text-success me-2"></i>Place orders</li>
                        <li><i class="fas fa-check text-success me-2"></i>Leave reviews</li>
                        <li><i class="fas fa-check text-success me-2"></i>Track orders</li>
                    </ul>
                </div>

                <div class="role-info" id="seller-info" style="display: none;">
                    <div class="text-primary mb-2">
                        <i class="fas fa-store fa-2x"></i>
                    </div>
                    <h6>Seller Account</h6>
                    <p class="text-muted small">
                        Sellers can list and sell products/services after verification.
                        They have access to seller dashboard, analytics, and order management.
                    </p>
                    <ul class="list-unstyled small">
                        <li><i class="fas fa-check text-success me-2"></i>List products/services</li>
                        <li><i class="fas fa-check text-success me-2"></i>Manage orders</li>
                        <li><i class="fas fa-check text-success me-2"></i>View analytics</li>
                        <li><i class="fas fa-exclamation-triangle text-warning me-2"></i>Requires verification</li>
                    </ul>
                </div>

                <div class="role-info" id="admin-info" style="display: none;">
                    <div class="text-danger mb-2">
                        <i class="fas fa-crown fa-2x"></i>
                    </div>
                    <h6>Admin Account</h6>
                    <p class="text-muted small">
                        Administrators have full system access including user management,
                        seller verification, dispute resolution, and system settings.
                    </p>
                    <ul class="list-unstyled small">
                        <li><i class="fas fa-check text-success me-2"></i>Full system access</li>
                        <li><i class="fas fa-check text-success me-2"></i>User management</li>
                        <li><i class="fas fa-check text-success me-2"></i>Seller verification</li>
                        <li><i class="fas fa-check text-success me-2"></i>System settings</li>
                    </ul>
                </div>

                <div class="role-info" id="default-info">
                    <div class="text-muted mb-2">
                        <i class="fas fa-user fa-2x"></i>
                    </div>
                    <h6>Select a Role</h6>
                    <p class="text-muted small">
                        Choose a role to see the permissions and capabilities for this user account.
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const roleSelect = document.getElementById('role');

    roleSelect.addEventListener('change', function() {
        const role = this.value;
        const infoDivs = document.querySelectorAll('.role-info');

        infoDivs.forEach(div => div.style.display = 'none');

        if (role) {
            const targetDiv = document.getElementById(role + '-info');
            if (targetDiv) {
                targetDiv.style.display = 'block';
            }
        } else {
            document.getElementById('default-info').style.display = 'block';
        }
    });

    // Auto-generate display name from first and last name
    document.getElementById('first_name').addEventListener('input', generateDisplayName);
    document.getElementById('last_name').addEventListener('input', generateDisplayName);

    function generateDisplayName() {
        const firstName = document.getElementById('first_name').value;
        const lastName = document.getElementById('last_name').value;
        const displayName = document.getElementById('name');

        if (firstName || lastName) {
            displayName.value = (firstName + ' ' + lastName).trim();
        }
    }
});
</script>
@endpush
