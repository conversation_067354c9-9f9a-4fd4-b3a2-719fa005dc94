@extends('admin.layout')

@section('title', 'Order Management')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-shopping-cart me-2"></i>Order Management</h2>
    <div class="btn-group">
        <a href="{{ route('admin.orders.index') }}" class="btn btn-outline-primary {{ !request('status') ? 'active' : '' }}">All Orders</a>
        <a href="{{ route('admin.orders.index', ['status' => 'pending']) }}" class="btn btn-outline-warning {{ request('status') == 'pending' ? 'active' : '' }}">Pending</a>
        <a href="{{ route('admin.orders.index', ['status' => 'processing']) }}" class="btn btn-outline-info {{ request('status') == 'processing' ? 'active' : '' }}">Processing</a>
        <a href="{{ route('admin.orders.index', ['status' => 'shipped']) }}" class="btn btn-outline-primary {{ request('status') == 'shipped' ? 'active' : '' }}">Shipped</a>
        <a href="{{ route('admin.orders.index', ['status' => 'delivered']) }}" class="btn btn-outline-success {{ request('status') == 'delivered' ? 'active' : '' }}">Delivered</a>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-2 mb-3">
        <div class="card stat-card text-center">
            <div class="card-body">
                <div class="text-primary mb-2">
                    <i class="fas fa-shopping-cart fa-2x"></i>
                </div>
                <h3 class="mb-0">{{ $stats['total_orders'] }}</h3>
                <small class="text-muted">Total Orders</small>
            </div>
        </div>
    </div>

    <div class="col-md-2 mb-3">
        <div class="card stat-card text-center">
            <div class="card-body">
                <div class="text-warning mb-2">
                    <i class="fas fa-clock fa-2x"></i>
                </div>
                <h3 class="mb-0">{{ $stats['pending_orders'] }}</h3>
                <small class="text-muted">Pending</small>
            </div>
        </div>
    </div>

    <div class="col-md-2 mb-3">
        <div class="card stat-card text-center">
            <div class="card-body">
                <div class="text-info mb-2">
                    <i class="fas fa-cog fa-2x"></i>
                </div>
                <h3 class="mb-0">{{ $stats['processing_orders'] }}</h3>
                <small class="text-muted">Processing</small>
            </div>
        </div>
    </div>

    <div class="col-md-2 mb-3">
        <div class="card stat-card text-center">
            <div class="card-body">
                <div class="text-primary mb-2">
                    <i class="fas fa-truck fa-2x"></i>
                </div>
                <h3 class="mb-0">{{ $stats['shipped_orders'] }}</h3>
                <small class="text-muted">Shipped</small>
            </div>
        </div>
    </div>

    <div class="col-md-2 mb-3">
        <div class="card stat-card text-center">
            <div class="card-body">
                <div class="text-success mb-2">
                    <i class="fas fa-check-circle fa-2x"></i>
                </div>
                <h3 class="mb-0">{{ $stats['delivered_orders'] }}</h3>
                <small class="text-muted">Delivered</small>
            </div>
        </div>
    </div>

    <div class="col-md-2 mb-3">
        <div class="card stat-card text-center">
            <div class="card-body">
                <div class="text-success mb-2">
                    <i class="fas fa-dollar-sign fa-2x"></i>
                </div>
                <h3 class="mb-0">${{ number_format($stats['total_revenue'], 2) }}</h3>
                <small class="text-muted">Total Revenue</small>
            </div>
        </div>
    </div>
</div>

<!-- Search and Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" action="{{ route('admin.orders.index') }}" class="row g-3">
            <div class="col-md-4">
                <label for="search" class="form-label">Search Orders</label>
                <input type="text" class="form-control" id="search" name="search"
                       value="{{ request('search') }}" placeholder="Order number, buyer, seller...">
            </div>
            <div class="col-md-3">
                <label for="status" class="form-label">Status</label>
                <select class="form-select" id="status" name="status">
                    <option value="">All Statuses</option>
                    <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>Pending</option>
                    <option value="processing" {{ request('status') == 'processing' ? 'selected' : '' }}>Processing</option>
                    <option value="shipped" {{ request('status') == 'shipped' ? 'selected' : '' }}>Shipped</option>
                    <option value="delivered" {{ request('status') == 'delivered' ? 'selected' : '' }}>Delivered</option>
                    <option value="cancelled" {{ request('status') == 'cancelled' ? 'selected' : '' }}>Cancelled</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="date_range" class="form-label">Date Range</label>
                <select class="form-select" id="date_range" name="date_range">
                    <option value="">All Time</option>
                    <option value="today">Today</option>
                    <option value="week">This Week</option>
                    <option value="month">This Month</option>
                </select>
            </div>
            <div class="col-md-2 d-flex align-items-end">
                <button type="submit" class="btn btn-primary me-2">
                    <i class="fas fa-search"></i> Search
                </button>
                <a href="{{ route('admin.orders.index') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-times"></i> Clear
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Orders Table -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">Orders List</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>Order #</th>
                        <th>Buyer</th>
                        <th>Seller</th>
                        <th>Items</th>
                        <th>Total</th>
                        <th>Status</th>
                        <th>Date</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($orders as $order)
                        <tr>
                            <td>
                                <strong>{{ $order->order_number }}</strong>
                            </td>
                            <td>{{ $order->buyer_name }}</td>
                            <td>{{ $order->seller_name }}</td>
                            <td>{{ $order->items_count }} items</td>
                            <td>${{ number_format($order->total_amount, 2) }}</td>
                            <td>
                                @php
                                    $statusClasses = [
                                        'pending' => 'warning',
                                        'processing' => 'info',
                                        'shipped' => 'primary',
                                        'delivered' => 'success',
                                        'cancelled' => 'danger'
                                    ];
                                @endphp
                                <span class="badge bg-{{ $statusClasses[$order->status] ?? 'secondary' }}">
                                    {{ ucfirst($order->status) }}
                                </span>
                            </td>
                            <td>{{ $order->created_at->format('M j, Y g:i A') }}</td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <a href="{{ route('admin.orders.show', $order->id) }}"
                                       class="btn btn-outline-primary" title="View Details">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <button type="button" class="btn btn-outline-secondary dropdown-toggle dropdown-toggle-split"
                                            data-bs-toggle="dropdown">
                                        <span class="visually-hidden">Toggle Dropdown</span>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="{{ route('admin.orders.show', $order->id) }}">
                                            <i class="fas fa-eye me-2"></i>View Details
                                        </a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item" href="#" onclick="updateStatus('{{ $order->id }}', 'processing')">
                                            <i class="fas fa-cog me-2"></i>Mark Processing
                                        </a></li>
                                        <li><a class="dropdown-item" href="#" onclick="updateStatus('{{ $order->id }}', 'shipped')">
                                            <i class="fas fa-truck me-2"></i>Mark Shipped
                                        </a></li>
                                        <li><a class="dropdown-item" href="#" onclick="updateStatus('{{ $order->id }}', 'delivered')">
                                            <i class="fas fa-check-circle me-2"></i>Mark Delivered
                                        </a></li>
                                    </ul>
                                </div>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="8" class="text-center py-4">
                                <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
                                <p class="text-muted">No orders found matching your criteria.</p>
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function updateStatus(orderId, status) {
    if (confirm('Are you sure you want to update this order status?')) {
        // In a real implementation, you would make an AJAX call to update the status
        console.log('Updating order', orderId, 'to status', status);
        // For now, just show a success message
        alert('Order status would be updated to: ' + status);
    }
}
</script>
@endpush
