<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $__env->yieldContent('title', 'Admin Dashboard'); ?> - Marketplace</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
        }
        .sidebar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
            position: fixed;
            top: 0;
            left: 0;
            width: 250px;
            z-index: 1000;
        }
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            border-radius: 8px;
            margin: 2px 0;
            padding: 12px 16px;
            transition: all 0.3s ease;
        }
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            background-color: rgba(255, 255, 255, 0.1);
            color: white;
            transform: translateX(5px);
        }
        .main-content {
            margin-left: 250px;
            padding: 20px;
        }
        .stat-card {
            border-radius: 15px;
            border: none;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            transition: transform 0.3s ease;
        }
        .stat-card:hover {
            transform: translateY(-5px);
        }
        .navbar-admin {
            background: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-left: 250px;
            position: fixed;
            top: 0;
            right: 0;
            left: 250px;
            z-index: 999;
            height: 60px;
        }
        .content-wrapper {
            margin-top: 80px;
        }
        .sidebar-brand {
            padding: 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            margin-bottom: 20px;
        }
        .sidebar-user {
            padding: 15px 20px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            margin-top: auto;
        }
        .table th {
            border-top: none;
            font-weight: 600;
            color: #495057;
        }
        .btn-group-sm > .btn {
            padding: 0.25rem 0.5rem;
            font-size: 0.875rem;
        }
    </style>
    <?php echo $__env->yieldPushContent('styles'); ?>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-brand text-center">
            <h4><i class="fas fa-crown me-2"></i>Admin Panel</h4>
        </div>

        <nav class="nav flex-column px-3">
            <a class="nav-link <?php echo e(request()->routeIs('admin.dashboard') ? 'active' : ''); ?>"
               href="<?php echo e(route('admin.dashboard')); ?>">
                <i class="fas fa-tachometer-alt me-2"></i>Dashboard
            </a>

            <a class="nav-link <?php echo e(request()->routeIs('admin.users.*') ? 'active' : ''); ?>"
               href="<?php echo e(route('admin.users.index')); ?>">
                <i class="fas fa-users me-2"></i>Users Management
            </a>

            <a class="nav-link <?php echo e(request()->routeIs('admin.sellers.*') ? 'active' : ''); ?>"
               href="<?php echo e(route('admin.sellers.index')); ?>">
                <i class="fas fa-store me-2"></i>Sellers Management
            </a>

            <a class="nav-link <?php echo e(request()->routeIs('admin.orders.*') ? 'active' : ''); ?>"
               href="<?php echo e(route('admin.orders.index')); ?>">
                <i class="fas fa-shopping-cart me-2"></i>Orders
            </a>

            <a class="nav-link <?php echo e(request()->routeIs('admin.products.*') ? 'active' : ''); ?>"
               href="<?php echo e(route('admin.products.index')); ?>">
                <i class="fas fa-box me-2"></i>Products
            </a>

            <a class="nav-link <?php echo e(request()->routeIs('admin.disputes.*') ? 'active' : ''); ?>"
               href="<?php echo e(route('admin.disputes.index')); ?>">
                <i class="fas fa-exclamation-triangle me-2"></i>Disputes
            </a>

            <a class="nav-link <?php echo e(request()->routeIs('admin.analytics.*') ? 'active' : ''); ?>"
               href="<?php echo e(route('admin.analytics')); ?>">
                <i class="fas fa-chart-bar me-2"></i>Analytics
            </a>

            <a class="nav-link <?php echo e(request()->routeIs('admin.settings.*') ? 'active' : ''); ?>"
               href="<?php echo e(route('admin.settings')); ?>">
                <i class="fas fa-cog me-2"></i>Settings
            </a>
        </nav>

        <div class="sidebar-user">
            <div class="d-flex align-items-center mb-2">
                <div class="avatar-circle me-2">
                    <?php echo e(strtoupper(substr(auth()->user()->name, 0, 2))); ?>

                </div>
                <div>
                    <small class="d-block"><?php echo e(auth()->user()->name); ?></small>
                    <small class="text-muted">Administrator</small>
                </div>
            </div>
            <form method="POST" action="<?php echo e(route('logout')); ?>">
                <?php echo csrf_field(); ?>
                <button type="submit" class="btn btn-outline-light btn-sm w-100">
                    <i class="fas fa-sign-out-alt me-2"></i>Logout
                </button>
            </form>
        </div>
    </div>

    <!-- Top Navbar -->
    <nav class="navbar navbar-admin navbar-expand-lg navbar-light">
        <div class="container-fluid">
            <div class="d-flex align-items-center">
                <h5 class="mb-0"><?php echo $__env->yieldContent('title', 'Dashboard'); ?></h5>
            </div>

            <div class="d-flex align-items-center">
                <div class="text-muted me-3">
                    <i class="fas fa-calendar me-1"></i><?php echo e(now()->format('F j, Y')); ?>

                </div>

                <!-- Notifications -->
                <div class="dropdown me-3">
                    <button class="btn btn-outline-secondary btn-sm" type="button" data-bs-toggle="dropdown">
                        <i class="fas fa-bell"></i>
                        <span class="badge bg-danger badge-sm">3</span>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><h6 class="dropdown-header">Notifications</h6></li>
                        <li><a class="dropdown-item" href="#">New seller registration</a></li>
                        <li><a class="dropdown-item" href="#">Dispute reported</a></li>
                        <li><a class="dropdown-item" href="#">System update available</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item text-center" href="#">View all</a></li>
                    </ul>
                </div>

                <!-- Quick Actions -->
                <div class="dropdown">
                    <button class="btn btn-primary btn-sm" type="button" data-bs-toggle="dropdown">
                        <i class="fas fa-plus me-1"></i>Quick Actions
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="<?php echo e(route('admin.users.create')); ?>">
                            <i class="fas fa-user-plus me-2"></i>Add User
                        </a></li>
                        <li><a class="dropdown-item" href="<?php echo e(route('admin.orders.index')); ?>">
                            <i class="fas fa-shopping-cart me-2"></i>View Orders
                        </a></li>
                        <li><a class="dropdown-item" href="<?php echo e(route('admin.products.index')); ?>">
                            <i class="fas fa-box me-2"></i>Manage Products
                        </a></li>
                        <li><a class="dropdown-item" href="<?php echo e(route('admin.disputes.index')); ?>">
                            <i class="fas fa-exclamation-triangle me-2"></i>View Disputes
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="main-content">
        <div class="content-wrapper">
            <!-- Flash Messages -->
            <?php if(session('success')): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i><?php echo e(session('success')); ?>

                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if(session('error')): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-circle me-2"></i><?php echo e(session('error')); ?>

                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if(session('warning')): ?>
                <div class="alert alert-warning alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i><?php echo e(session('warning')); ?>

                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php echo $__env->yieldContent('content'); ?>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <?php echo $__env->yieldPushContent('scripts'); ?>
</body>
</html>
<?php /**PATH C:\xampp11\htdocs\bll\resources\views/admin/layout.blade.php ENDPATH**/ ?>