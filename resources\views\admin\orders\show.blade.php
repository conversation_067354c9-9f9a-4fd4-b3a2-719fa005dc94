@extends('admin.layout')

@section('title', 'Order Details - ' . $order->order_number)

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2><i class="fas fa-shopping-cart me-2"></i>Order Details</h2>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
                <li class="breadcrumb-item"><a href="{{ route('admin.orders.index') }}">Orders</a></li>
                <li class="breadcrumb-item active">{{ $order->order_number }}</li>
            </ol>
        </nav>
    </div>
    <div class="btn-group">
        <button type="button" class="btn btn-primary dropdown-toggle" data-bs-toggle="dropdown">
            <i class="fas fa-cog me-1"></i>Update Status
        </button>
        <ul class="dropdown-menu">
            <li><a class="dropdown-item" href="#" onclick="updateOrderStatus('pending')">
                <i class="fas fa-clock me-2 text-warning"></i>Pending
            </a></li>
            <li><a class="dropdown-item" href="#" onclick="updateOrderStatus('processing')">
                <i class="fas fa-cog me-2 text-info"></i>Processing
            </a></li>
            <li><a class="dropdown-item" href="#" onclick="updateOrderStatus('shipped')">
                <i class="fas fa-truck me-2 text-primary"></i>Shipped
            </a></li>
            <li><a class="dropdown-item" href="#" onclick="updateOrderStatus('delivered')">
                <i class="fas fa-check-circle me-2 text-success"></i>Delivered
            </a></li>
            <li><hr class="dropdown-divider"></li>
            <li><a class="dropdown-item" href="#" onclick="updateOrderStatus('cancelled')">
                <i class="fas fa-times-circle me-2 text-danger"></i>Cancel Order
            </a></li>
        </ul>
    </div>
</div>

<!-- Order Summary -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Order Information</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-muted">Order Details</h6>
                        <p><strong>Order Number:</strong> {{ $order->order_number }}</p>
                        <p><strong>Order Date:</strong> {{ $order->created_at->format('M j, Y g:i A') }}</p>
                        <p><strong>Payment Method:</strong> {{ $order->payment_method }}</p>
                        <p><strong>Total Amount:</strong> <span class="text-success">${{ number_format($order->total_amount, 2) }}</span></p>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-muted">Shipping Address</h6>
                        <p>{{ $order->shipping_address }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Order Status</h5>
            </div>
            <div class="card-body text-center">
                @php
                    $statusClasses = [
                        'pending' => 'warning',
                        'processing' => 'info',
                        'shipped' => 'primary',
                        'delivered' => 'success',
                        'cancelled' => 'danger'
                    ];
                    $statusIcons = [
                        'pending' => 'clock',
                        'processing' => 'cog',
                        'shipped' => 'truck',
                        'delivered' => 'check-circle',
                        'cancelled' => 'times-circle'
                    ];
                @endphp
                <div class="text-{{ $statusClasses[$order->status] ?? 'secondary' }} mb-3">
                    <i class="fas fa-{{ $statusIcons[$order->status] ?? 'question' }} fa-3x"></i>
                </div>
                <h4 class="text-{{ $statusClasses[$order->status] ?? 'secondary' }}">
                    {{ ucfirst($order->status) }}
                </h4>
                <small class="text-muted">Last updated: {{ $order->created_at->diffForHumans() }}</small>
            </div>
        </div>
    </div>
</div>

<!-- Customer Information -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Buyer Information</h5>
            </div>
            <div class="card-body">
                <div class="d-flex align-items-center mb-3">
                    <div class="avatar-circle me-3">
                        {{ strtoupper(substr($order->buyer_name, 0, 2)) }}
                    </div>
                    <div>
                        <h6 class="mb-0">{{ $order->buyer_name }}</h6>
                        <small class="text-muted">{{ $order->buyer_email }}</small>
                    </div>
                </div>
                <div class="btn-group btn-group-sm">
                    <a href="mailto:{{ $order->buyer_email }}" class="btn btn-outline-primary">
                        <i class="fas fa-envelope me-1"></i>Email
                    </a>
                    <button type="button" class="btn btn-outline-info">
                        <i class="fas fa-user me-1"></i>View Profile
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Seller Information</h5>
            </div>
            <div class="card-body">
                <div class="d-flex align-items-center mb-3">
                    <div class="avatar-circle me-3">
                        {{ strtoupper(substr($order->seller_name, 0, 2)) }}
                    </div>
                    <div>
                        <h6 class="mb-0">{{ $order->seller_name }}</h6>
                        <small class="text-muted">{{ $order->seller_email }}</small>
                    </div>
                </div>
                <div class="btn-group btn-group-sm">
                    <a href="mailto:{{ $order->seller_email }}" class="btn btn-outline-primary">
                        <i class="fas fa-envelope me-1"></i>Email
                    </a>
                    <button type="button" class="btn btn-outline-info">
                        <i class="fas fa-store me-1"></i>View Store
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Order Items -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">Order Items</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table">
                <thead>
                    <tr>
                        <th>Product</th>
                        <th>Quantity</th>
                        <th>Unit Price</th>
                        <th>Total</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($order->items as $item)
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="product-image me-3">
                                        <div class="bg-light rounded" style="width: 50px; height: 50px; display: flex; align-items: center; justify-content: center;">
                                            <i class="fas fa-box text-muted"></i>
                                        </div>
                                    </div>
                                    <div>
                                        <h6 class="mb-0">{{ $item->name }}</h6>
                                        <small class="text-muted">SKU: PRD-{{ str_pad($loop->index + 1, 3, '0', STR_PAD_LEFT) }}</small>
                                    </div>
                                </div>
                            </td>
                            <td>{{ $item->quantity }}</td>
                            <td>${{ number_format($item->price / $item->quantity, 2) }}</td>
                            <td><strong>${{ number_format($item->price, 2) }}</strong></td>
                        </tr>
                    @endforeach
                </tbody>
                <tfoot>
                    <tr>
                        <th colspan="3" class="text-end">Total:</th>
                        <th class="text-success">${{ number_format($order->total_amount, 2) }}</th>
                    </tr>
                </tfoot>
            </table>
        </div>
    </div>
</div>

<!-- Order Timeline -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">Order Timeline</h5>
    </div>
    <div class="card-body">
        <div class="timeline">
            <div class="timeline-item">
                <div class="timeline-marker bg-success"></div>
                <div class="timeline-content">
                    <h6 class="mb-1">Order Placed</h6>
                    <p class="mb-0 text-muted">{{ $order->created_at->format('M j, Y g:i A') }}</p>
                </div>
            </div>
            
            @if($order->status != 'pending')
            <div class="timeline-item">
                <div class="timeline-marker bg-info"></div>
                <div class="timeline-content">
                    <h6 class="mb-1">Payment Confirmed</h6>
                    <p class="mb-0 text-muted">{{ $order->created_at->addHours(1)->format('M j, Y g:i A') }}</p>
                </div>
            </div>
            @endif
            
            @if(in_array($order->status, ['processing', 'shipped', 'delivered']))
            <div class="timeline-item">
                <div class="timeline-marker bg-warning"></div>
                <div class="timeline-content">
                    <h6 class="mb-1">Order Processing</h6>
                    <p class="mb-0 text-muted">{{ $order->created_at->addHours(2)->format('M j, Y g:i A') }}</p>
                </div>
            </div>
            @endif
            
            @if(in_array($order->status, ['shipped', 'delivered']))
            <div class="timeline-item">
                <div class="timeline-marker bg-primary"></div>
                <div class="timeline-content">
                    <h6 class="mb-1">Order Shipped</h6>
                    <p class="mb-0 text-muted">{{ $order->created_at->addDay()->format('M j, Y g:i A') }}</p>
                </div>
            </div>
            @endif
            
            @if($order->status == 'delivered')
            <div class="timeline-item">
                <div class="timeline-marker bg-success"></div>
                <div class="timeline-content">
                    <h6 class="mb-1">Order Delivered</h6>
                    <p class="mb-0 text-muted">{{ $order->created_at->addDays(3)->format('M j, Y g:i A') }}</p>
                </div>
            </div>
            @endif
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -22px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid #fff;
    box-shadow: 0 0 0 2px #dee2e6;
}

.timeline-content {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    border-left: 3px solid #007bff;
}

.avatar-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 14px;
}
</style>
@endpush

@push('scripts')
<script>
function updateOrderStatus(status) {
    if (confirm('Are you sure you want to update this order status to: ' + status + '?')) {
        // In a real implementation, you would make an AJAX call or form submission
        console.log('Updating order status to:', status);
        alert('Order status would be updated to: ' + status);
    }
}
</script>
@endpush
