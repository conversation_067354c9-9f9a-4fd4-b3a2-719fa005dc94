<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('reviews', function (Blueprint $table) {
            $table->id();
            $table->foreignId('order_id')->constrained()->onDelete('cascade');
            $table->foreignId('reviewer_id')->constrained('users')->onDelete('cascade'); // Who wrote the review
            $table->foreignId('reviewee_id')->constrained('users')->onDelete('cascade'); // Who is being reviewed
            $table->foreignId('product_id')->constrained()->onDelete('cascade');
            $table->integer('rating'); // 1-5 stars
            $table->text('comment')->nullable();
            $table->json('rating_breakdown')->nullable(); // Communication, Quality, Delivery, etc.
            $table->boolean('is_public')->default(true);
            $table->boolean('is_featured')->default(false);
            $table->text('seller_response')->nullable(); // Seller can respond to reviews
            $table->timestamp('seller_responded_at')->nullable();
            $table->boolean('is_verified')->default(true); // Verified purchase
            $table->json('metadata')->nullable(); // Additional review data
            $table->timestamps();

            // Ensure one review per order
            $table->unique(['order_id', 'reviewer_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('reviews');
    }
};
