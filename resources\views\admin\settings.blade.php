@extends('admin.layout')

@section('title', 'System Settings')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-cog me-2"></i>System Settings</h2>
    <button type="button" class="btn btn-primary" onclick="saveAllSettings()">
        <i class="fas fa-save me-2"></i>Save All Changes
    </button>
</div>

<div class="row">
    <!-- General Settings -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">General Settings</h5>
            </div>
            <div class="card-body">
                <form id="generalSettings">
                    <div class="mb-3">
                        <label class="form-label">Site Name</label>
                        <input type="text" class="form-control" name="site_name" value="Marketplace">
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Site Description</label>
                        <textarea class="form-control" name="site_description" rows="3">A trusted marketplace connecting buyers and sellers worldwide</textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Contact Email</label>
                        <input type="email" class="form-control" name="contact_email" value="<EMAIL>">
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Support Phone</label>
                        <input type="tel" class="form-control" name="support_phone" value="******-567-8900">
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Default Language</label>
                        <select class="form-select" name="default_language">
                            <option value="en" selected>English</option>
                            <option value="es">Spanish</option>
                            <option value="fr">French</option>
                            <option value="de">German</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Default Timezone</label>
                        <select class="form-select" name="default_timezone">
                            <option value="UTC" selected>UTC</option>
                            <option value="America/New_York">Eastern Time</option>
                            <option value="America/Chicago">Central Time</option>
                            <option value="America/Denver">Mountain Time</option>
                            <option value="America/Los_Angeles">Pacific Time</option>
                        </select>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Marketplace Settings -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Marketplace Settings</h5>
            </div>
            <div class="card-body">
                <form id="marketplaceSettings">
                    <div class="mb-3">
                        <label class="form-label">Commission Rate (%)</label>
                        <input type="number" class="form-control" name="commission_rate" value="5" min="0" max="50" step="0.1">
                        <small class="text-muted">Platform commission on each sale</small>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Minimum Order Amount ($)</label>
                        <input type="number" class="form-control" name="min_order_amount" value="10" min="0" step="0.01">
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Maximum Order Amount ($)</label>
                        <input type="number" class="form-control" name="max_order_amount" value="10000" min="0" step="0.01">
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" name="auto_approve_sellers" id="autoApproveSellers">
                            <label class="form-check-label" for="autoApproveSellers">
                                Auto-approve new sellers
                            </label>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" name="require_seller_verification" id="requireVerification" checked>
                            <label class="form-check-label" for="requireVerification">
                                Require seller verification
                            </label>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" name="allow_guest_checkout" id="allowGuestCheckout">
                            <label class="form-check-label" for="allowGuestCheckout">
                                Allow guest checkout
                            </label>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Security Settings -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Security Settings</h5>
            </div>
            <div class="card-body">
                <form id="securitySettings">
                    <div class="mb-3">
                        <label class="form-label">Password Minimum Length</label>
                        <input type="number" class="form-control" name="password_min_length" value="8" min="6" max="20">
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Login Attempt Limit</label>
                        <input type="number" class="form-control" name="login_attempt_limit" value="5" min="3" max="10">
                        <small class="text-muted">Maximum failed login attempts before lockout</small>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Session Timeout (minutes)</label>
                        <input type="number" class="form-control" name="session_timeout" value="120" min="30" max="480">
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" name="require_email_verification" id="requireEmailVerification" checked>
                            <label class="form-check-label" for="requireEmailVerification">
                                Require email verification
                            </label>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" name="enable_two_factor" id="enableTwoFactor">
                            <label class="form-check-label" for="enableTwoFactor">
                                Enable two-factor authentication
                            </label>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" name="log_user_activities" id="logActivities" checked>
                            <label class="form-check-label" for="logActivities">
                                Log user activities
                            </label>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Email Settings -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Email Settings</h5>
            </div>
            <div class="card-body">
                <form id="emailSettings">
                    <div class="mb-3">
                        <label class="form-label">SMTP Host</label>
                        <input type="text" class="form-control" name="smtp_host" value="smtp.gmail.com">
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">SMTP Port</label>
                        <input type="number" class="form-control" name="smtp_port" value="587">
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">SMTP Username</label>
                        <input type="email" class="form-control" name="smtp_username" placeholder="<EMAIL>">
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">From Email</label>
                        <input type="email" class="form-control" name="from_email" value="<EMAIL>">
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">From Name</label>
                        <input type="text" class="form-control" name="from_name" value="Marketplace">
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" name="send_welcome_emails" id="sendWelcomeEmails" checked>
                            <label class="form-check-label" for="sendWelcomeEmails">
                                Send welcome emails
                            </label>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" name="send_order_notifications" id="sendOrderNotifications" checked>
                            <label class="form-check-label" for="sendOrderNotifications">
                                Send order notifications
                            </label>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Payment Settings -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Payment Settings</h5>
            </div>
            <div class="card-body">
                <form id="paymentSettings">
                    <div class="mb-3">
                        <label class="form-label">Default Currency</label>
                        <select class="form-select" name="default_currency">
                            <option value="USD" selected>USD - US Dollar</option>
                            <option value="EUR">EUR - Euro</option>
                            <option value="GBP">GBP - British Pound</option>
                            <option value="CAD">CAD - Canadian Dollar</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" name="enable_stripe" id="enableStripe" checked>
                            <label class="form-check-label" for="enableStripe">
                                Enable Stripe payments
                            </label>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" name="enable_paypal" id="enablePaypal">
                            <label class="form-check-label" for="enablePaypal">
                                Enable PayPal payments
                            </label>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" name="enable_bank_transfer" id="enableBankTransfer">
                            <label class="form-check-label" for="enableBankTransfer">
                                Enable bank transfers
                            </label>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Payment Processing Fee (%)</label>
                        <input type="number" class="form-control" name="payment_fee" value="2.9" min="0" max="10" step="0.1">
                        <small class="text-muted">Additional fee for payment processing</small>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Maintenance Mode -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Maintenance Mode</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>Warning:</strong> Enabling maintenance mode will make the site unavailable to users.
                </div>
                
                <form id="maintenanceSettings">
                    <div class="mb-3">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" name="maintenance_mode" id="maintenanceMode">
                            <label class="form-check-label" for="maintenanceMode">
                                Enable maintenance mode
                            </label>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Maintenance Message</label>
                        <textarea class="form-control" name="maintenance_message" rows="3">We are currently performing scheduled maintenance. Please check back soon.</textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Estimated Completion</label>
                        <input type="datetime-local" class="form-control" name="maintenance_end">
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Save Confirmation Modal -->
<div class="modal fade" id="saveModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Settings Saved</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="text-center">
                    <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                    <h5>Settings Updated Successfully</h5>
                    <p class="text-muted">All system settings have been saved and applied.</p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" data-bs-dismiss="modal">OK</button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function saveAllSettings() {
    // Simulate saving settings
    setTimeout(() => {
        new bootstrap.Modal(document.getElementById('saveModal')).show();
    }, 500);
    
    // In a real application, you would collect all form data and send it to the server
    console.log('Saving all settings...');
}

// Auto-save functionality
document.addEventListener('DOMContentLoaded', function() {
    const forms = document.querySelectorAll('form');
    
    forms.forEach(form => {
        const inputs = form.querySelectorAll('input, select, textarea');
        
        inputs.forEach(input => {
            input.addEventListener('change', function() {
                // Show a small indicator that settings have changed
                const saveButton = document.querySelector('button[onclick="saveAllSettings()"]');
                if (!saveButton.classList.contains('btn-warning')) {
                    saveButton.classList.remove('btn-primary');
                    saveButton.classList.add('btn-warning');
                    saveButton.innerHTML = '<i class="fas fa-exclamation-triangle me-2"></i>Save Changes';
                }
            });
        });
    });
});
</script>
@endpush
