<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('messages', function (Blueprint $table) {
            $table->id();
            $table->foreignId('sender_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('receiver_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('order_id')->nullable()->constrained()->onDelete('cascade'); // Order-specific messages
            $table->string('conversation_id'); // Group messages by conversation
            $table->text('message');
            $table->json('attachments')->nullable(); // File attachments
            $table->enum('type', ['text', 'file', 'system', 'order_update'])->default('text');
            $table->boolean('is_read')->default(false);
            $table->timestamp('read_at')->nullable();
            $table->boolean('is_important')->default(false);
            $table->json('metadata')->nullable(); // Additional message data
            $table->timestamps();

            // Index for better performance
            $table->index(['sender_id', 'receiver_id']);
            $table->index(['conversation_id']);
            $table->index(['order_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('messages');
    }
};
