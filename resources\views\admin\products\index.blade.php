@extends('admin.layout')

@section('title', 'Product Management')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-box me-2"></i>Product Management</h2>
    <div class="btn-group">
        <a href="{{ route('admin.products.index') }}" class="btn btn-outline-primary {{ !request('status') ? 'active' : '' }}">All Products</a>
        <a href="{{ route('admin.products.index', ['status' => 'active']) }}" class="btn btn-outline-success {{ request('status') == 'active' ? 'active' : '' }}">Active</a>
        <a href="{{ route('admin.products.index', ['status' => 'pending']) }}" class="btn btn-outline-warning {{ request('status') == 'pending' ? 'active' : '' }}">Pending</a>
        <a href="{{ route('admin.products.index', ['status' => 'inactive']) }}" class="btn btn-outline-secondary {{ request('status') == 'inactive' ? 'active' : '' }}">Inactive</a>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-2 mb-3">
        <div class="card stat-card text-center">
            <div class="card-body">
                <div class="text-primary mb-2">
                    <i class="fas fa-box fa-2x"></i>
                </div>
                <h3 class="mb-0">{{ $stats['total_products'] }}</h3>
                <small class="text-muted">Total Products</small>
            </div>
        </div>
    </div>

    <div class="col-md-2 mb-3">
        <div class="card stat-card text-center">
            <div class="card-body">
                <div class="text-success mb-2">
                    <i class="fas fa-check-circle fa-2x"></i>
                </div>
                <h3 class="mb-0">{{ $stats['active_products'] }}</h3>
                <small class="text-muted">Active</small>
            </div>
        </div>
    </div>

    <div class="col-md-2 mb-3">
        <div class="card stat-card text-center">
            <div class="card-body">
                <div class="text-warning mb-2">
                    <i class="fas fa-clock fa-2x"></i>
                </div>
                <h3 class="mb-0">{{ $stats['pending_products'] }}</h3>
                <small class="text-muted">Pending</small>
            </div>
        </div>
    </div>

    <div class="col-md-2 mb-3">
        <div class="card stat-card text-center">
            <div class="card-body">
                <div class="text-secondary mb-2">
                    <i class="fas fa-pause-circle fa-2x"></i>
                </div>
                <h3 class="mb-0">{{ $stats['inactive_products'] }}</h3>
                <small class="text-muted">Inactive</small>
            </div>
        </div>
    </div>

    <div class="col-md-2 mb-3">
        <div class="card stat-card text-center">
            <div class="card-body">
                <div class="text-danger mb-2">
                    <i class="fas fa-exclamation-triangle fa-2x"></i>
                </div>
                <h3 class="mb-0">{{ $stats['low_stock_products'] }}</h3>
                <small class="text-muted">Low Stock</small>
            </div>
        </div>
    </div>
</div>

<!-- Search and Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" action="{{ route('admin.products.index') }}" class="row g-3">
            <div class="col-md-4">
                <label for="search" class="form-label">Search Products</label>
                <input type="text" class="form-control" id="search" name="search"
                       value="{{ request('search') }}" placeholder="Product name, seller, category...">
            </div>
            <div class="col-md-2">
                <label for="status" class="form-label">Status</label>
                <select class="form-select" id="status" name="status">
                    <option value="">All Statuses</option>
                    <option value="active" {{ request('status') == 'active' ? 'selected' : '' }}>Active</option>
                    <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>Pending</option>
                    <option value="inactive" {{ request('status') == 'inactive' ? 'selected' : '' }}>Inactive</option>
                    <option value="rejected" {{ request('status') == 'rejected' ? 'selected' : '' }}>Rejected</option>
                </select>
            </div>
            <div class="col-md-2">
                <label for="category" class="form-label">Category</label>
                <select class="form-select" id="category" name="category">
                    <option value="">All Categories</option>
                    @foreach($categories as $category)
                        <option value="{{ $category }}" {{ request('category') == $category ? 'selected' : '' }}>
                            {{ $category }}
                        </option>
                    @endforeach
                </select>
            </div>
            <div class="col-md-2">
                <label for="sort" class="form-label">Sort By</label>
                <select class="form-select" id="sort" name="sort">
                    <option value="created_at">Newest First</option>
                    <option value="name">Name A-Z</option>
                    <option value="price_low">Price Low-High</option>
                    <option value="price_high">Price High-Low</option>
                </select>
            </div>
            <div class="col-md-2 d-flex align-items-end">
                <button type="submit" class="btn btn-primary me-2">
                    <i class="fas fa-search"></i> Search
                </button>
                <a href="{{ route('admin.products.index') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-times"></i> Clear
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Products Grid -->
<div class="row">
    @forelse($products as $product)
        <div class="col-md-6 col-lg-4 mb-4">
            <div class="card product-card h-100">
                <div class="position-relative">
                    <img src="{{ $product->image }}" class="card-img-top" alt="{{ $product->name }}" style="height: 200px; object-fit: cover;">
                    <div class="position-absolute top-0 end-0 m-2">
                        @php
                            $statusClasses = [
                                'active' => 'success',
                                'pending' => 'warning',
                                'inactive' => 'secondary',
                                'rejected' => 'danger'
                            ];
                        @endphp
                        <span class="badge bg-{{ $statusClasses[$product->status] ?? 'secondary' }}">
                            {{ ucfirst($product->status) }}
                        </span>
                    </div>
                    @if($product->stock <= 5)
                        <div class="position-absolute top-0 start-0 m-2">
                            <span class="badge bg-danger">Low Stock</span>
                        </div>
                    @endif
                </div>

                <div class="card-body d-flex flex-column">
                    <h6 class="card-title">{{ $product->name }}</h6>
                    <p class="text-muted small mb-2">
                        <i class="fas fa-store me-1"></i>{{ $product->seller_name }}
                    </p>
                    <p class="text-muted small mb-2">
                        <i class="fas fa-tag me-1"></i>{{ $product->category }}
                    </p>

                    <div class="mt-auto">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div>
                                <h5 class="text-success mb-0">${{ number_format($product->price, 2) }}</h5>
                                <small class="text-muted">Stock: {{ $product->stock }}</small>
                            </div>
                        </div>

                        <div class="btn-group w-100">
                            <a href="{{ route('admin.products.show', $product->id) }}"
                               class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-eye"></i> View
                            </a>

                            @if($product->status == 'pending')
                                <button type="button" class="btn btn-outline-success btn-sm"
                                        onclick="approveProduct({{ $product->id }})">
                                    <i class="fas fa-check"></i> Approve
                                </button>
                                <button type="button" class="btn btn-outline-danger btn-sm"
                                        onclick="rejectProduct({{ $product->id }})">
                                    <i class="fas fa-times"></i> Reject
                                </button>
                            @else
                                <div class="dropdown">
                                    <button class="btn btn-outline-secondary btn-sm dropdown-toggle"
                                            type="button" data-bs-toggle="dropdown">
                                        <i class="fas fa-cog"></i> Actions
                                    </button>
                                    <ul class="dropdown-menu">
                                        @if($product->status == 'active')
                                            <li><a class="dropdown-item" href="#" onclick="updateStatus({{ $product->id }}, 'inactive')">
                                                <i class="fas fa-pause me-2"></i>Deactivate
                                            </a></li>
                                        @else
                                            <li><a class="dropdown-item" href="#" onclick="updateStatus({{ $product->id }}, 'active')">
                                                <i class="fas fa-play me-2"></i>Activate
                                            </a></li>
                                        @endif
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item text-danger" href="#" onclick="deleteProduct({{ $product->id }})">
                                            <i class="fas fa-trash me-2"></i>Delete
                                        </a></li>
                                    </ul>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    @empty
        <div class="col-12">
            <div class="text-center py-5">
                <i class="fas fa-box fa-4x text-muted mb-3"></i>
                <h4 class="text-muted">No Products Found</h4>
                <p class="text-muted">No products match your current search criteria.</p>
                <a href="{{ route('admin.products.index') }}" class="btn btn-primary">
                    <i class="fas fa-redo me-1"></i>Clear Filters
                </a>
            </div>
        </div>
    @endforelse
</div>

<!-- Bulk Actions Modal -->
<div class="modal fade" id="bulkActionsModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Bulk Actions</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Select an action to perform on selected products:</p>
                <div class="d-grid gap-2">
                    <button type="button" class="btn btn-success" onclick="bulkApprove()">
                        <i class="fas fa-check me-2"></i>Approve Selected
                    </button>
                    <button type="button" class="btn btn-warning" onclick="bulkDeactivate()">
                        <i class="fas fa-pause me-2"></i>Deactivate Selected
                    </button>
                    <button type="button" class="btn btn-danger" onclick="bulkDelete()">
                        <i class="fas fa-trash me-2"></i>Delete Selected
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.product-card {
    transition: transform 0.2s;
}

.product-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.stat-card {
    transition: transform 0.2s;
}

.stat-card:hover {
    transform: translateY(-2px);
}
</style>
@endpush

@push('scripts')
<script>
function approveProduct(productId) {
    if (confirm('Are you sure you want to approve this product?')) {
        // In a real implementation, make AJAX call to approve
        console.log('Approving product:', productId);
        alert('Product would be approved');
    }
}

function rejectProduct(productId) {
    const reason = prompt('Please provide a reason for rejection:');
    if (reason) {
        // In a real implementation, make AJAX call to reject with reason
        console.log('Rejecting product:', productId, 'Reason:', reason);
        alert('Product would be rejected with reason: ' + reason);
    }
}

function updateStatus(productId, status) {
    if (confirm('Are you sure you want to update this product status?')) {
        // In a real implementation, make AJAX call to update status
        console.log('Updating product:', productId, 'to status:', status);
        alert('Product status would be updated to: ' + status);
    }
}

function deleteProduct(productId) {
    if (confirm('Are you sure you want to delete this product? This action cannot be undone.')) {
        // In a real implementation, make AJAX call to delete
        console.log('Deleting product:', productId);
        alert('Product would be deleted');
    }
}
</script>
@endpush
