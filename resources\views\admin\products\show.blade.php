@extends('admin.layout')

@section('title', 'Product Details - ' . $product->name)

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2><i class="fas fa-box me-2"></i>Product Details</h2>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
                <li class="breadcrumb-item"><a href="{{ route('admin.products.index') }}">Products</a></li>
                <li class="breadcrumb-item active">{{ $product->name }}</li>
            </ol>
        </nav>
    </div>
    <div class="btn-group">
        @if($product->status == 'pending')
            <button type="button" class="btn btn-success" onclick="approveProduct()">
                <i class="fas fa-check me-1"></i>Approve
            </button>
            <button type="button" class="btn btn-danger" onclick="rejectProduct()">
                <i class="fas fa-times me-1"></i>Reject
            </button>
        @else
            <button type="button" class="btn btn-primary dropdown-toggle" data-bs-toggle="dropdown">
                <i class="fas fa-cog me-1"></i>Actions
            </button>
            <ul class="dropdown-menu">
                @if($product->status == 'active')
                    <li><a class="dropdown-item" href="#" onclick="updateStatus('inactive')">
                        <i class="fas fa-pause me-2"></i>Deactivate
                    </a></li>
                @else
                    <li><a class="dropdown-item" href="#" onclick="updateStatus('active')">
                        <i class="fas fa-play me-2"></i>Activate
                    </a></li>
                @endif
                <li><hr class="dropdown-divider"></li>
                <li><a class="dropdown-item text-danger" href="#" onclick="deleteProduct()">
                    <i class="fas fa-trash me-2"></i>Delete Product
                </a></li>
            </ul>
        @endif
    </div>
</div>

<!-- Product Status Alert -->
@if($product->status == 'pending')
    <div class="alert alert-warning" role="alert">
        <i class="fas fa-clock me-2"></i>
        <strong>Pending Approval:</strong> This product is waiting for admin approval before it can be listed.
    </div>
@elseif($product->status == 'inactive')
    <div class="alert alert-secondary" role="alert">
        <i class="fas fa-pause me-2"></i>
        <strong>Inactive:</strong> This product is currently not visible to customers.
    </div>
@elseif($product->status == 'rejected')
    <div class="alert alert-danger" role="alert">
        <i class="fas fa-times me-2"></i>
        <strong>Rejected:</strong> This product has been rejected and is not available for sale.
    </div>
@endif

<!-- Product Overview -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Product Images</h5>
            </div>
            <div class="card-body">
                <div id="productCarousel" class="carousel slide" data-bs-ride="carousel">
                    <div class="carousel-inner">
                        @foreach($product->images as $index => $image)
                            <div class="carousel-item {{ $index == 0 ? 'active' : '' }}">
                                <img src="{{ $image }}" class="d-block w-100" alt="Product Image {{ $index + 1 }}" style="height: 300px; object-fit: cover;">
                            </div>
                        @endforeach
                    </div>
                    @if(count($product->images) > 1)
                        <button class="carousel-control-prev" type="button" data-bs-target="#productCarousel" data-bs-slide="prev">
                            <span class="carousel-control-prev-icon"></span>
                        </button>
                        <button class="carousel-control-next" type="button" data-bs-target="#productCarousel" data-bs-slide="next">
                            <span class="carousel-control-next-icon"></span>
                        </button>
                    @endif
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Product Information</h5>
            </div>
            <div class="card-body">
                <h4 class="mb-3">{{ $product->name }}</h4>
                
                <div class="row mb-3">
                    <div class="col-sm-4"><strong>Price:</strong></div>
                    <div class="col-sm-8">
                        <h5 class="text-success mb-0">${{ number_format($product->price, 2) }}</h5>
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-sm-4"><strong>Status:</strong></div>
                    <div class="col-sm-8">
                        @php
                            $statusClasses = [
                                'active' => 'success',
                                'pending' => 'warning',
                                'inactive' => 'secondary',
                                'rejected' => 'danger'
                            ];
                        @endphp
                        <span class="badge bg-{{ $statusClasses[$product->status] ?? 'secondary' }}">
                            {{ ucfirst($product->status) }}
                        </span>
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-sm-4"><strong>Category:</strong></div>
                    <div class="col-sm-8">{{ $product->category }}</div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-sm-4"><strong>SKU:</strong></div>
                    <div class="col-sm-8">{{ $product->sku }}</div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-sm-4"><strong>Stock:</strong></div>
                    <div class="col-sm-8">
                        <span class="badge bg-{{ $product->stock > 10 ? 'success' : ($product->stock > 0 ? 'warning' : 'danger') }}">
                            {{ $product->stock }} units
                        </span>
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-sm-4"><strong>Weight:</strong></div>
                    <div class="col-sm-8">{{ $product->weight }}</div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-sm-4"><strong>Dimensions:</strong></div>
                    <div class="col-sm-8">{{ $product->dimensions }}</div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-sm-4"><strong>Listed:</strong></div>
                    <div class="col-sm-8">{{ $product->created_at->format('M j, Y g:i A') }}</div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Product Description -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Product Description</h5>
            </div>
            <div class="card-body">
                <p>{{ $product->description }}</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Seller Information</h5>
            </div>
            <div class="card-body">
                <div class="d-flex align-items-center mb-3">
                    <div class="avatar-circle me-3">
                        {{ strtoupper(substr($product->seller_name, 0, 2)) }}
                    </div>
                    <div>
                        <h6 class="mb-0">{{ $product->seller_name }}</h6>
                        <small class="text-muted">{{ $product->seller_email }}</small>
                    </div>
                </div>
                <div class="btn-group btn-group-sm w-100">
                    <a href="mailto:{{ $product->seller_email }}" class="btn btn-outline-primary">
                        <i class="fas fa-envelope me-1"></i>Email
                    </a>
                    <button type="button" class="btn btn-outline-info">
                        <i class="fas fa-store me-1"></i>View Store
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Product Specifications -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">Product Specifications</h5>
    </div>
    <div class="card-body">
        <div class="row">
            @foreach($product->specifications as $key => $value)
                <div class="col-md-6 mb-3">
                    <div class="d-flex justify-content-between">
                        <strong>{{ $key }}:</strong>
                        <span>{{ $value }}</span>
                    </div>
                </div>
            @endforeach
        </div>
    </div>
</div>

<!-- Product Analytics -->
<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Product Performance</h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-4">
                        <h4 class="text-primary">0</h4>
                        <small class="text-muted">Views</small>
                    </div>
                    <div class="col-4">
                        <h4 class="text-success">0</h4>
                        <small class="text-muted">Orders</small>
                    </div>
                    <div class="col-4">
                        <h4 class="text-info">0</h4>
                        <small class="text-muted">Favorites</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Recent Activity</h5>
            </div>
            <div class="card-body">
                <div class="timeline">
                    <div class="timeline-item">
                        <div class="timeline-marker bg-success"></div>
                        <div class="timeline-content">
                            <h6 class="mb-1">Product Listed</h6>
                            <p class="mb-0 text-muted">{{ $product->created_at->format('M j, Y g:i A') }}</p>
                        </div>
                    </div>
                    
                    @if($product->status != 'pending')
                    <div class="timeline-item">
                        <div class="timeline-marker bg-info"></div>
                        <div class="timeline-content">
                            <h6 class="mb-1">Status Updated</h6>
                            <p class="mb-0 text-muted">{{ $product->created_at->addHours(2)->format('M j, Y g:i A') }}</p>
                        </div>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Rejection Modal -->
<div class="modal fade" id="rejectModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Reject Product</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="rejectForm">
                    <div class="mb-3">
                        <label for="rejectionReason" class="form-label">Reason for Rejection</label>
                        <textarea class="form-control" id="rejectionReason" rows="4" 
                                  placeholder="Please provide a detailed reason for rejecting this product..." required></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" onclick="confirmReject()">Reject Product</button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -22px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid #fff;
    box-shadow: 0 0 0 2px #dee2e6;
}

.timeline-content {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    border-left: 3px solid #007bff;
}

.avatar-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 14px;
}
</style>
@endpush

@push('scripts')
<script>
function approveProduct() {
    if (confirm('Are you sure you want to approve this product?')) {
        // In a real implementation, make AJAX call to approve
        console.log('Approving product');
        alert('Product would be approved');
    }
}

function rejectProduct() {
    const modal = new bootstrap.Modal(document.getElementById('rejectModal'));
    modal.show();
}

function confirmReject() {
    const reason = document.getElementById('rejectionReason').value;
    if (reason.trim()) {
        // In a real implementation, make AJAX call to reject with reason
        console.log('Rejecting product with reason:', reason);
        alert('Product would be rejected with reason: ' + reason);
        bootstrap.Modal.getInstance(document.getElementById('rejectModal')).hide();
    } else {
        alert('Please provide a reason for rejection');
    }
}

function updateStatus(status) {
    if (confirm('Are you sure you want to update this product status to: ' + status + '?')) {
        // In a real implementation, make AJAX call to update status
        console.log('Updating product status to:', status);
        alert('Product status would be updated to: ' + status);
    }
}

function deleteProduct() {
    if (confirm('Are you sure you want to delete this product? This action cannot be undone.')) {
        // In a real implementation, make AJAX call to delete
        console.log('Deleting product');
        alert('Product would be deleted');
    }
}
</script>
@endpush
